﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet1" targetNamespace="http://tempuri.org/DataSet1.xsd" xmlns:mstns="http://tempuri.org/DataSet1.xsd" xmlns="http://tempuri.org/DataSet1.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="SIS_DBConnectionString1" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="SIS_DBConnectionString1 (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Sales_and_Inventory_System.My.MySettings.GlobalReference.Default.SIS_DBConnectionString1" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SalesManComessionTableAdapter" GeneratorDataComponentClassName="SalesManComessionTableAdapter" Name="SalesManComession" UserDataComponentName="SalesManComessionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.SalesManComession" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        SM_ID, SalesMan_ID, Name, Address, City, State, ZipCode, ContactNo, EmailID, Remarks, Photo, CommissionPer, Amount, SalesMan_Comession, Comession, Date, 
                         TC_ID
FROM            SalesManComession</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="SM_ID" DataSetColumn="SM_ID" />
              <Mapping SourceColumn="SalesMan_ID" DataSetColumn="SalesMan_ID" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="State" DataSetColumn="State" />
              <Mapping SourceColumn="ZipCode" DataSetColumn="ZipCode" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
              <Mapping SourceColumn="Photo" DataSetColumn="Photo" />
              <Mapping SourceColumn="CommissionPer" DataSetColumn="CommissionPer" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="SalesMan_Comession" DataSetColumn="SalesMan_Comession" />
              <Mapping SourceColumn="Comession" DataSetColumn="Comession" />
              <Mapping SourceColumn="Date" DataSetColumn="Date" />
              <Mapping SourceColumn="TC_ID" DataSetColumn="TC_ID" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet1" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSet1" msprop:Generator_UserDSName="DataSet1">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="SalesManComession" msprop:Generator_TableClassName="SalesManComessionDataTable" msprop:Generator_TableVarName="tableSalesManComession" msprop:Generator_RowChangedName="SalesManComessionRowChanged" msprop:Generator_TablePropName="SalesManComession" msprop:Generator_RowDeletingName="SalesManComessionRowDeleting" msprop:Generator_RowChangingName="SalesManComessionRowChanging" msprop:Generator_RowEvHandlerName="SalesManComessionRowChangeEventHandler" msprop:Generator_RowDeletedName="SalesManComessionRowDeleted" msprop:Generator_RowClassName="SalesManComessionRow" msprop:Generator_UserTableName="SalesManComession" msprop:Generator_RowEvArgName="SalesManComessionRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SM_ID" msprop:Generator_ColumnVarNameInTable="columnSM_ID" msprop:Generator_ColumnPropNameInRow="SM_ID" msprop:Generator_ColumnPropNameInTable="SM_IDColumn" msprop:Generator_UserColumnName="SM_ID" type="xs:int" />
              <xs:element name="SalesMan_ID" msprop:Generator_ColumnVarNameInTable="columnSalesMan_ID" msprop:Generator_ColumnPropNameInRow="SalesMan_ID" msprop:Generator_ColumnPropNameInTable="SalesMan_IDColumn" msprop:Generator_UserColumnName="SalesMan_ID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_UserColumnName="Address" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_UserColumnName="City" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="State" msprop:Generator_ColumnVarNameInTable="columnState" msprop:Generator_ColumnPropNameInRow="State" msprop:Generator_ColumnPropNameInTable="StateColumn" msprop:Generator_UserColumnName="State" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ZipCode" msprop:Generator_ColumnVarNameInTable="columnZipCode" msprop:Generator_ColumnPropNameInRow="ZipCode" msprop:Generator_ColumnPropNameInTable="ZipCodeColumn" msprop:Generator_UserColumnName="ZipCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Photo" msprop:Generator_ColumnVarNameInTable="columnPhoto" msprop:Generator_ColumnPropNameInRow="Photo" msprop:Generator_ColumnPropNameInTable="PhotoColumn" msprop:Generator_UserColumnName="Photo" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="CommissionPer" msprop:Generator_ColumnVarNameInTable="columnCommissionPer" msprop:Generator_ColumnPropNameInRow="CommissionPer" msprop:Generator_ColumnPropNameInTable="CommissionPerColumn" msprop:Generator_UserColumnName="CommissionPer" type="xs:decimal" minOccurs="0" />
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="SalesMan_Comession" msprop:Generator_ColumnVarNameInTable="columnSalesMan_Comession" msprop:Generator_ColumnPropNameInRow="SalesMan_Comession" msprop:Generator_ColumnPropNameInTable="SalesMan_ComessionColumn" msprop:Generator_UserColumnName="SalesMan_Comession" type="xs:decimal" minOccurs="0" />
              <xs:element name="Comession" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnComession" msprop:Generator_ColumnPropNameInRow="Comession" msprop:Generator_ColumnPropNameInTable="ComessionColumn" msprop:Generator_UserColumnName="Comession" type="xs:decimal" minOccurs="0" />
              <xs:element name="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="_Date" msprop:Generator_ColumnPropNameInTable="DateColumn" msprop:Generator_UserColumnName="Date" type="xs:dateTime" />
              <xs:element name="TC_ID" msprop:Generator_ColumnVarNameInTable="columnTC_ID" msprop:Generator_ColumnPropNameInRow="TC_ID" msprop:Generator_ColumnPropNameInTable="TC_IDColumn" msprop:Generator_UserColumnName="TC_ID" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:SalesManComession" />
      <xs:field xpath="mstns:SM_ID" />
      <xs:field xpath="mstns:TC_ID" />
    </xs:unique>
  </xs:element>
</xs:schema>