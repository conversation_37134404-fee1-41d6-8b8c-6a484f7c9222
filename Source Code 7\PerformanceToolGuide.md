# دليل استخدام أداة فحص الأداء

## نظرة عامة

تم تطوير أداة شاملة لفحص أداء نظام المبيعات والمخزون تتكون من عدة مكونات:

1. **PerformanceAnalyzer.vb** - محلل الأداء الأساسي
2. **CodeAnalyzer.vb** - محلل جودة الكود
3. **SystemHealthChecker.vb** - فاحص الصحة العامة للنظام
4. **frmPerformanceReport.vb** - واجهة المستخدم

## كيفية إضافة الأداة إلى النظام

### الخطوة 1: إضافة الملفات إلى المشروع

1. انسخ الملفات التالية إلى مجلد المشروع:
   - `PerformanceAnalyzer.vb`
   - `CodeAnalyzer.vb`
   - `SystemHealthChecker.vb`
   - `frmPerformanceReport.vb`
   - `frmPerformanceReport.Designer.vb`

2. أضف الملفات إلى المشروع في Visual Studio:
   - انقر بالزر الأيمن على المشروع
   - اختر "Add" → "Existing Item"
   - حدد الملفات المنسوخة

### الخطوة 2: إضافة المراجع المطلوبة

تأكد من وجود المراجع التالية في المشروع:
```xml
<Reference Include="System.Management" />
<Reference Include="System.Data.SqlClient" />
<Reference Include="System.Threading.Tasks" />
```

### الخطوة 3: إضافة القائمة إلى النموذج الرئيسي

أضف الكود التالي إلى `frmMainMenu.vb`:

```vb
Private Sub PerformanceAnalysisToolStripMenuItem_Click(sender As System.Object, e As System.EventArgs) Handles PerformanceAnalysisToolStripMenuItem.Click
    Try
        Dim frmPerf As New frmPerformanceReport()
        frmPerf.ShowDialog()
    Catch ex As Exception
        MessageBox.Show($"خطأ في فتح أداة فحص الأداء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
    End Try
End Sub
```

### الخطوة 4: إضافة عنصر القائمة

في مصمم النموذج الرئيسي، أضف عنصر قائمة جديد:
1. افتح `frmMainMenu` في المصمم
2. أضف عنصر قائمة جديد تحت قائمة "Tools" أو "أدوات"
3. اضبط الخصائص:
   - Name: `PerformanceAnalysisToolStripMenuItem`
   - Text: `فحص الأداء`

## كيفية الاستخدام

### 1. فحص الأداء الأساسي

- انقر على زر "فحص الأداء"
- انتظر حتى انتهاء التحليل
- راجع النتائج في مربع النص

**ما يتم فحصه:**
- استخدام المعالج والذاكرة
- أداء قاعدة البيانات
- الاستعلامات البطيئة
- حجم الملفات

### 2. الفحص الشامل

- انقر على زر "فحص شامل"
- انتظر (قد يستغرق عدة دقائق)
- احصل على تقرير مفصل

**ما يتم فحصه:**
- جميع عناصر الفحص الأساسي
- تحليل جودة الكود
- فحص الأمان
- تحليل بنية المشروع
- التوصيات والخطط

### 3. حفظ وطباعة التقارير

- استخدم زر "حفظ التقرير" لحفظ النتائج
- استخدم زر "طباعة" لطباعة التقرير
- يمكن حفظ التقرير بصيغة نصية

## اختصارات لوحة المفاتيح

- `Ctrl + S`: حفظ التقرير
- `Ctrl + P`: طباعة التقرير
- `Ctrl + F5`: بدء فحص الأداء
- `Escape`: إغلاق النافذة

## فهم نتائج التحليل

### 1. تحليل الأداء

```
استخدام المعالج: 15.23%        // طبيعي إذا كان أقل من 80%
الذاكرة المتاحة: 2048 MB       // يجب أن تكون كافية
استخدام القرص الصلب: 45.67%   // طبيعي إذا كان أقل من 90%
```

### 2. قاعدة البيانات

```
حجم قاعدة البيانات: 125.45 MB  // راقب النمو
عدد الجداول: 25               // معقول للنظام
أكبر الجداول:                 // راقب الجداول الكبيرة
  - Sales: 15000 صف، 12.5 MB
```

### 3. مشاكل الكود

```
مشاكل عالية الخطورة: 5         // يجب إصلاحها فوراً
مشاكل متوسطة الخطورة: 12       // خطط لإصلاحها
مشاكل منخفضة الخطورة: 8        // حسن عند الإمكان
```

## التحليل والإجراءات

### مؤشرات الأداء الجيد

✅ **استخدام المعالج**: أقل من 50%
✅ **الذاكرة المتاحة**: أكثر من 1 GB
✅ **وقت الاستجابة**: أقل من 2 ثانية
✅ **مشاكل عالية الخطورة**: 0

### مؤشرات تحتاج انتباه

⚠️ **استخدام المعالج**: 50-80%
⚠️ **الذاكرة المتاحة**: 500MB - 1GB
⚠️ **وقت الاستجابة**: 2-5 ثواني
⚠️ **مشاكل عالية الخطورة**: 1-5

### مؤشرات خطيرة

🔴 **استخدام المعالج**: أكثر من 80%
🔴 **الذاكرة المتاحة**: أقل من 500MB
🔴 **وقت الاستجابة**: أكثر من 5 ثواني
🔴 **مشاكل عالية الخطورة**: أكثر من 5

## الإجراءات المقترحة

### للأداء البطيء
1. أعد تشغيل التطبيق
2. تحقق من الاستعلامات البطيئة
3. أضف فهارس لقاعدة البيانات
4. نظف الملفات المؤقتة

### لمشاكل الذاكرة
1. أغلق التطبيقات غير المستخدمة
2. تحقق من تسريب الذاكرة في الكود
3. استخدم Using statements
4. قم بتحرير الموارد بشكل صحيح

### لمشاكل قاعدة البيانات
1. قم بعمل نسخة احتياطية
2. أعد بناء الفهارس
3. نظف البيانات القديمة
4. حسن الاستعلامات

## استكشاف الأخطاء

### خطأ: "لا يمكن الاتصال بقاعدة البيانات"
- تحقق من ملف `SQLSettings.dat`
- تأكد من تشغيل SQL Server
- تحقق من صلاحيات المستخدم

### خطأ: "نفاد الذاكرة"
- أغلق التطبيقات الأخرى
- أعد تشغيل النظام
- تحقق من تسريب الذاكرة

### خطأ: "الوصول مرفوض"
- شغل التطبيق كمدير
- تحقق من صلاحيات الملفات
- تأكد من عدم حماية الملفات بكلمة مرور

## الصيانة الدورية

### يومياً
- راقب استخدام الموارد
- تحقق من رسائل الخطأ

### أسبوعياً
- شغل فحص الأداء الأساسي
- راجع سجلات النظام

### شهرياً
- شغل الفحص الشامل
- احفظ تقرير للمقارنة
- خطط للتحسينات

### سنوياً
- راجع جميع التوصيات
- حدث النظام والمكتبات
- قيم الحاجة لترقيات الأجهزة

## الدعم والمساعدة

إذا واجهت مشاكل في استخدام الأداة:
1. راجع هذا الدليل
2. تحقق من سجلات الأخطاء
3. اتصل بفريق الدعم التقني
4. احفظ تقرير الخطأ للمراجعة
