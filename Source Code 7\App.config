﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="Sales_and_Inventory_System.My.MySettings"
    type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"
    allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
		</sectionGroup>
	<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
	<section name="entityFramework"
   type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089"
   requirePermission="false"/>
	<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
    <connectionStrings />
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/>
  </startup>
  <userSettings>
    <Sales_and_Inventory_System.My.MySettings>
      <setting name="checked" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="day" serializeAs="String">
        <value />
      </setting>
      <setting name="month" serializeAs="String">
        <value />
      </setting>
      <setting name="year" serializeAs="String">
        <value />
      </setting>
      <setting name="setting" serializeAs="String">
        <value />
      </setting>
      <setting name="sdate" serializeAs="String">
        <value />
      </setting>
      <setting name="user" serializeAs="String">
        <value />
      </setting>
      <setting name="LOG" serializeAs="String">
        <value>C:\logo.png</value>
      </setting>
      <setting name="NAME" serializeAs="String">
        <value>برنامج حسابات تكنوستان المجاني 1</value>
      </setting>
      <setting name="PHONE" serializeAs="String">
        <value />
      </setting>
      <setting name="MOBILE" serializeAs="String">
        <value />
      </setting>
      <setting name="EMAIL" serializeAs="String">
        <value />
      </setting>
      <setting name="WEB" serializeAs="String">
        <value />
      </setting>
      <setting name="SERVER" serializeAs="String">
        <value />
      </setting>
      <setting name="DATABASE" serializeAs="String">
        <value />
      </setting>
      <setting name="Name_SA" serializeAs="String">
        <value>sa</value>
      </setting>
      <setting name="Pas_SA" serializeAs="String">
        <value />
      </setting>
      <setting name="Server_Name" serializeAs="String">
        <value />
      </setting>
      <setting name="ServerCn" serializeAs="String">
        <value />
      </setting>
      <setting name="Name_User" serializeAs="String">
        <value />
      </setting>
      <setting name="cccc" serializeAs="String">
        <value />
      </setting>
    </Sales_and_Inventory_System.My.MySettings>
	</userSettings>
  <entityFramework>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
      <provider invariantName="System.Data.SqlServerCe.4.0" type="System.Data.Entity.SqlServerCompact.SqlCeProviderServices, EntityFramework.SqlServerCompact"/>
    </providers>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlCeConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="System.Data.SqlServerCe.4.0"/>
      </parameters>
    </defaultConnectionFactory>
  </entityFramework>
<system.data>
    <DbProviderFactories>
      <remove invariant="System.Data.SqlServerCe.4.0"/>
      <add name="Microsoft SQL Server Compact Data Provider 4.0" invariant="System.Data.SqlServerCe.4.0"
          description=".NET Framework Data Provider for Microsoft SQL Server Compact"
          type="System.Data.SqlServerCe.SqlCeProviderFactory, System.Data.SqlServerCe, Version=*******, Culture=neutral, PublicKeyToken=89845dcd8080cc91"/>
    </DbProviderFactories>
  </system.data></configuration>

