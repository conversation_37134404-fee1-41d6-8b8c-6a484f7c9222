Imports System.IO
Imports System.Text.RegularExpressions
Imports System.Text

Public Class CodeAnalyzer
    Private results As New StringBuilder()
    Private codeIssues As New List(Of CodeIssue)
    
    Public Structure CodeIssue
        Public FileName As String
        Public LineNumber As Integer
        Public IssueType As String
        Public Description As String
        Public Severity As String
        Public Recommendation As String
    End Structure
    
    Public Function AnalyzeCode(projectPath As String) As String
        results.Clear()
        codeIssues.Clear()
        
        results.AppendLine("=== تحليل جودة الكود ونقاط الضعف ===")
        results.AppendLine($"تاريخ التحليل: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
        results.AppendLine($"مسار المشروع: {projectPath}")
        results.AppendLine(New String("=", 60))
        
        ' تحليل ملفات VB.NET
        AnalyzeVBFiles(projectPath)
        
        ' تحليل ملفات قاعدة البيانات
        AnalyzeDatabaseFiles(projectPath)
        
        ' تحليل ملفات الإعدادات
        AnalyzeConfigFiles(projectPath)
        
        ' إنشاء التقرير النهائي
        GenerateCodeReport()
        
        Return results.ToString()
    End Function
    
    Private Sub AnalyzeVBFiles(projectPath As String)
        results.AppendLine(vbCrLf & "1. تحليل ملفات VB.NET:")
        results.AppendLine(New String("-", 30))
        
        Try
            Dim vbFiles As String() = Directory.GetFiles(projectPath, "*.vb", SearchOption.TopDirectoryOnly)
            results.AppendLine($"عدد ملفات VB.NET: {vbFiles.Length}")
            
            Dim totalLines As Integer = 0
            Dim totalMethods As Integer = 0
            Dim totalClasses As Integer = 0
            
            For Each filePath As String In vbFiles
                If Not filePath.Contains(".Designer.vb") Then
                    AnalyzeSingleVBFile(filePath, totalLines, totalMethods, totalClasses)
                End If
            Next
            
            results.AppendLine($"إجمالي الأسطر: {totalLines}")
            results.AppendLine($"إجمالي الطرق: {totalMethods}")
            results.AppendLine($"إجمالي الكلاسات: {totalClasses}")
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل ملفات VB.NET: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeSingleVBFile(filePath As String, ByRef totalLines As Integer, ByRef totalMethods As Integer, ByRef totalClasses As Integer)
        Try
            Dim fileName As String = Path.GetFileName(filePath)
            Dim lines As String() = File.ReadAllLines(filePath)
            totalLines += lines.Length
            
            Dim lineNumber As Integer = 0
            Dim methodCount As Integer = 0
            Dim classCount As Integer = 0
            
            For Each line As String In lines
                lineNumber += 1
                Dim trimmedLine As String = line.Trim()
                
                ' تحليل الكلاسات
                If Regex.IsMatch(trimmedLine, "^(Public|Private|Protected|Friend)?\s*(Class|Module)\s+\w+", RegexOptions.IgnoreCase) Then
                    classCount += 1
                    totalClasses += 1
                End If
                
                ' تحليل الطرق
                If Regex.IsMatch(trimmedLine, "^(Public|Private|Protected|Friend)?\s*(Sub|Function)\s+\w+", RegexOptions.IgnoreCase) Then
                    methodCount += 1
                    totalMethods += 1
                End If
                
                ' فحص المشاكل الشائعة
                CheckCommonIssues(fileName, lineNumber, trimmedLine)
            Next
            
            ' فحص حجم الملف
            If lines.Length > 1000 Then
                AddIssue(fileName, 0, "حجم الملف", $"الملف كبير جداً ({lines.Length} سطر)", "متوسط", "فكر في تقسيم الملف إلى ملفات أصغر")
            End If
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل الملف {filePath}: {ex.Message}")
        End Try
    End Sub
    
    Private Sub CheckCommonIssues(fileName As String, lineNumber As Integer, line As String)
        ' فحص استخدام Try-Catch
        If line.ToLower().Contains("executereader()") OrElse line.ToLower().Contains("executenonquery()") Then
            If Not line.ToLower().Contains("try") Then
                AddIssue(fileName, lineNumber, "معالجة الأخطاء", "استعلام قاعدة بيانات بدون Try-Catch", "عالي", "استخدم Try-Catch لمعالجة أخطاء قاعدة البيانات")
            End If
        End If
        
        ' فحص إدارة الموارد
        If line.ToLower().Contains("new sqlconnection") AndAlso Not line.ToLower().Contains("using") Then
            AddIssue(fileName, lineNumber, "إدارة الموارد", "SqlConnection بدون Using statement", "عالي", "استخدم Using statement لضمان إغلاق الاتصال")
        End If
        
        ' فحص SQL Injection
        If line.Contains("\"") AndAlso line.ToLower().Contains("select") AndAlso line.Contains("&") Then
            AddIssue(fileName, lineNumber, "أمان", "احتمالية SQL Injection", "عالي", "استخدم Parameters بدلاً من تجميع النصوص")
        End If
        
        ' فحص كلمات المرور المكشوفة
        If line.ToLower().Contains("password") AndAlso line.Contains("=") AndAlso Not line.ToLower().Contains("inputbox") Then
            AddIssue(fileName, lineNumber, "أمان", "كلمة مرور محتملة في الكود", "عالي", "لا تضع كلمات المرور في الكود المصدري")
        End If
        
        ' فحص الطرق الطويلة
        If line.ToLower().StartsWith("public sub") OrElse line.ToLower().StartsWith("private sub") OrElse 
           line.ToLower().StartsWith("public function") OrElse line.ToLower().StartsWith("private function") Then
            ' هذا يحتاج تحليل أعمق لحساب طول الطريقة
        End If
        
        ' فحص المتغيرات غير المستخدمة
        If line.ToLower().StartsWith("dim ") AndAlso Not line.ToLower().Contains("as new") Then
            Dim varName As String = ExtractVariableName(line)
            If Not String.IsNullOrEmpty(varName) Then
                ' هذا يحتاج تحليل أعمق للتحقق من الاستخدام
            End If
        End If
        
        ' فحص التعليقات
        If line.Trim().StartsWith("'") AndAlso line.Length > 100 Then
            AddIssue(fileName, lineNumber, "جودة الكود", "تعليق طويل جداً", "منخفض", "اجعل التعليقات مختصرة ومفيدة")
        End If
        
        ' فحص الأسطر الطويلة
        If line.Length > 120 Then
            AddIssue(fileName, lineNumber, "جودة الكود", "سطر طويل جداً", "منخفض", "قسم السطر الطويل إلى عدة أسطر")
        End If
        
        ' فحص استخدام MessageBox بكثرة
        If line.ToLower().Contains("messagebox.show") Then
            AddIssue(fileName, lineNumber, "تجربة المستخدم", "استخدام MessageBox", "منخفض", "فكر في استخدام نظام إشعارات أفضل")
        End If
    End Sub
    
    Private Function ExtractVariableName(dimLine As String) As String
        Try
            Dim pattern As String = "dim\s+(\w+)"
            Dim match As Match = Regex.Match(dimLine, pattern, RegexOptions.IgnoreCase)
            If match.Success Then
                Return match.Groups(1).Value
            End If
        Catch
        End Try
        Return ""
    End Function
    
    Private Sub AddIssue(fileName As String, lineNumber As Integer, issueType As String, description As String, severity As String, recommendation As String)
        Dim issue As New CodeIssue With {
            .FileName = fileName,
            .LineNumber = lineNumber,
            .IssueType = issueType,
            .Description = description,
            .Severity = severity,
            .Recommendation = recommendation
        }
        codeIssues.Add(issue)
    End Sub
    
    Private Sub AnalyzeDatabaseFiles(projectPath As String)
        results.AppendLine(vbCrLf & "2. تحليل ملفات قاعدة البيانات:")
        results.AppendLine(New String("-", 30))
        
        Try
            ' فحص ملفات .mdf و .ldf
            Dim dbFiles As String() = Directory.GetFiles(projectPath, "*.mdf", SearchOption.AllDirectories)
            results.AppendLine($"ملفات قاعدة البيانات (.mdf): {dbFiles.Length}")
            
            For Each dbFile As String In dbFiles
                Dim fileInfo As New FileInfo(dbFile)
                results.AppendLine($"  - {fileInfo.Name}: {fileInfo.Length / 1024 / 1024:F2} MB")
                
                If fileInfo.Length > 100 * 1024 * 1024 Then ' أكبر من 100 MB
                    AddIssue(fileInfo.Name, 0, "حجم قاعدة البيانات", "قاعدة البيانات كبيرة الحجم", "متوسط", "فكر في تحسين قاعدة البيانات وحذف البيانات القديمة")
                End If
            Next
            
            ' فحص ملفات النسخ الاحتياطية
            Dim backupFiles As String() = Directory.GetFiles(projectPath, "*.bak", SearchOption.AllDirectories)
            results.AppendLine($"ملفات النسخ الاحتياطية (.bak): {backupFiles.Length}")
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل ملفات قاعدة البيانات: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeConfigFiles(projectPath As String)
        results.AppendLine(vbCrLf & "3. تحليل ملفات الإعدادات:")
        results.AppendLine(New String("-", 30))
        
        Try
            ' فحص App.config
            Dim appConfigPath As String = Path.Combine(projectPath, "App.config")
            If File.Exists(appConfigPath) Then
                results.AppendLine("✓ ملف App.config موجود")
                AnalyzeAppConfig(appConfigPath)
            Else
                results.AppendLine("✗ ملف App.config غير موجود")
                AddIssue("App.config", 0, "إعدادات", "ملف App.config مفقود", "متوسط", "أضف ملف App.config لإدارة الإعدادات")
            End If
            
            ' فحص ملف إعدادات قاعدة البيانات
            Dim sqlSettingsPath As String = Path.Combine(projectPath, "SQLSettings.dat")
            If File.Exists(sqlSettingsPath) Then
                results.AppendLine("✓ ملف SQLSettings.dat موجود")
                AnalyzeSQLSettings(sqlSettingsPath)
            Else
                results.AppendLine("✗ ملف SQLSettings.dat غير موجود")
            End If
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل ملفات الإعدادات: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeAppConfig(configPath As String)
        Try
            Dim configContent As String = File.ReadAllText(configPath)
            
            If configContent.ToLower().Contains("connectionstring") Then
                results.AppendLine("  - يحتوي على سلاسل اتصال")
                If configContent.ToLower().Contains("password") Then
                    AddIssue("App.config", 0, "أمان", "كلمة مرور في ملف الإعدادات", "عالي", "استخدم تشفير لكلمات المرور")
                End If
            End If
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل App.config: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeSQLSettings(settingsPath As String)
        Try
            Dim settingsContent As String = File.ReadAllText(settingsPath)
            
            If settingsContent.ToLower().Contains("password") Then
                AddIssue("SQLSettings.dat", 0, "أمان", "كلمة مرور غير مشفرة", "عالي", "شفّر ملف إعدادات قاعدة البيانات")
            End If
            
            If settingsContent.ToLower().Contains("sa") Then
                AddIssue("SQLSettings.dat", 0, "أمان", "استخدام حساب SA", "عالي", "استخدم حساب مستخدم محدود الصلاحيات")
            End If
            
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل SQLSettings.dat: {ex.Message}")
        End Try
    End Sub
    
    Private Sub GenerateCodeReport()
        results.AppendLine(vbCrLf & "4. ملخص المشاكل المكتشفة:")
        results.AppendLine(New String("-", 30))
        
        Dim highIssues = codeIssues.Where(Function(i) i.Severity = "عالي").Count()
        Dim mediumIssues = codeIssues.Where(Function(i) i.Severity = "متوسط").Count()
        Dim lowIssues = codeIssues.Where(Function(i) i.Severity = "منخفض").Count()
        
        results.AppendLine($"مشاكل عالية الخطورة: {highIssues}")
        results.AppendLine($"مشاكل متوسطة الخطورة: {mediumIssues}")
        results.AppendLine($"مشاكل منخفضة الخطورة: {lowIssues}")
        results.AppendLine($"إجمالي المشاكل: {codeIssues.Count}")
        
        If codeIssues.Count > 0 Then
            results.AppendLine(vbCrLf & "تفاصيل المشاكل:")
            
            ' ترتيب المشاكل حسب الخطورة
            Dim sortedIssues = codeIssues.OrderBy(Function(i) GetSeverityOrder(i.Severity)).ThenBy(Function(i) i.FileName)
            
            For Each issue In sortedIssues.Take(20) ' عرض أول 20 مشكلة
                results.AppendLine($"  [{issue.Severity}] {issue.FileName}")
                If issue.LineNumber > 0 Then
                    results.AppendLine($"    السطر {issue.LineNumber}: {issue.Description}")
                Else
                    results.AppendLine($"    {issue.Description}")
                End If
                results.AppendLine($"    التوصية: {issue.Recommendation}")
                results.AppendLine()
            Next
            
            If codeIssues.Count > 20 Then
                results.AppendLine($"... و {codeIssues.Count - 20} مشكلة أخرى")
            End If
        End If
    End Sub
    
    Private Function GetSeverityOrder(severity As String) As Integer
        Select Case severity
            Case "عالي"
                Return 1
            Case "متوسط"
                Return 2
            Case "منخفض"
                Return 3
            Case Else
                Return 4
        End Select
    End Function
End Class
