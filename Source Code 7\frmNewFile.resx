﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        sQAAALEBxi1JjQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIvSURBVEhLY8AD
        GIFYmY2ZOVxOhH+2ioTgFQ1JoVNAMTawLImAGYg1OViZ4+REBBYriApc1JIRexBorvW8I9bt57aauP93
        J+f/T7HT+i0vI3NATUVl8/l9u/2NDQyWmRsbHlKQlXWHGIMFyIsJLNKVl3gYYaP3oj/R8/eehsT/96YX
        /384swQFP5he+D/dUefPke2b/r+/f/P//3fP/39+fOf/i0tn/xvr6z2BGocJDBQkn6Abhg3DLLh0ZB/Y
        cDh+8fC/pYnxc6hxmMBQadQC+lkAStZ8YEORAbUs4OXnTxGXlnknIimZDjUaAvzNNK9hMxAdI1tw8fDe
        /231NRBcU/lfVlrqM5+g0KHpWw/8949LfisqLTMBaDTIRwwMx9vT1mAzEIZXlUT8z3Q3+39vWgHcAg9P
        z/+T1+9CwTO2Hfx/7O2f/yfe//uf19z9CeibLUDj2RmMlaQeYzMYGcc5GP7fWx8Pt8DN3QNsED7cunTD
        L2V948tExQGpFux48/d/3qKN/4AWXCFoASiIsj3NUYLIwcP7f+nKnSi4asOB/5te/v6/6dXf/5EtE/4p
        6OgfBQcROanoAjCSm+pqIbim6r+MtPQXUWm5syXr9v93Tc75o6BjMA9oOCSSqZVMxaTksySVVH8qaOoW
        gg2GAWpZADQK5GJesKHIgIoWYAdD3wKaVzjkVJnv7t2AY4JVJhSQVukrK28BYXVV1a0EK308AKPZoiMj
        fBwoRkKzhYEBANFegkB9BRnbAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="Button2.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        ZAAAAGQBeJH1SwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFdSURBVEhL5dS/
        SsRAEMfxFMeBhWJ5ImIjIsK9gChqJ9gI4iHHFWIhitiKfwr1AQ6tRFAbKx9AsdOzsTisfAOfJH4XMnGy
        Tm5XCy38wQeyM5sMCWGTNE17ImOYCxi27nXMoiAzeMJxDw94x4j5DKsoyCZWrZ4gOzjAMwa+9P2CRmIG
        bGMZ87hDpdDXCx+JGbCFlex6DVeFvl74SMyABi7hHu68opX39WYfiRlQwRJkQBt7eV9v9pHgAB9ZwD8a
        sIGG1StDvjWgD1WrV4bEDSA36GTOrT0WEj2gY12HkD8aQKqYhhzBL6rXVfUpFM4bjZQOOMUZ5Bhuqt66
        ql/gSHo+sojdfK0at6jJugyZgBvShPwEmnvber5f3XgNd3DJpxhXvUlVd2/Tll7I50WSjOIQ8im6qvem
        6vsYkl6IWXTIj/4in1l0fmPAI9wnOcG9tSeGWXTIIGYz/daesDT5ADKnIZLHODm4AAAAAElFTkSuQmCC
</value>
  </data>
</root>