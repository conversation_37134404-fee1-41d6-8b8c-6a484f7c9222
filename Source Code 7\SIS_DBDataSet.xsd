﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="SIS_DBDataSet" targetNamespace="http://tempuri.org/SIS_DBDataSet.xsd" xmlns:mstns="http://tempuri.org/SIS_DBDataSet.xsd" xmlns="http://tempuri.org/SIS_DBDataSet.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="SIS_DBConnectionString1" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="SIS_DBConnectionString1 (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.Sales_and_Inventory_System.My.MySettings.GlobalReference.Default.SIS_DBConnectionString1" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ActivationTableAdapter" GeneratorDataComponentClassName="ActivationTableAdapter" Name="Activation" UserDataComponentName="ActivationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Activation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Activation] WHERE (([ID] = @Original_ID) AND ([HardwareID] = @Original_HardwareID) AND ([SerialNo] = @Original_SerialNo) AND ([ActivationID] = @Original_ActivationID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_HardwareID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="HardwareID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SerialNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SerialNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ActivationID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ActivationID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Activation] ([HardwareID], [SerialNo], [ActivationID]) VALUES (@HardwareID, @SerialNo, @ActivationID);
SELECT ID, HardwareID, SerialNo, ActivationID FROM Activation WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@HardwareID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="HardwareID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SerialNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SerialNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ActivationID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ActivationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, HardwareID, SerialNo, ActivationID FROM dbo.Activation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Activation] SET [HardwareID] = @HardwareID, [SerialNo] = @SerialNo, [ActivationID] = @ActivationID WHERE (([ID] = @Original_ID) AND ([HardwareID] = @Original_HardwareID) AND ([SerialNo] = @Original_SerialNo) AND ([ActivationID] = @Original_ActivationID));
SELECT ID, HardwareID, SerialNo, ActivationID FROM Activation WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@HardwareID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="HardwareID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SerialNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SerialNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ActivationID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ActivationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_HardwareID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="HardwareID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SerialNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SerialNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ActivationID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ActivationID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="INV_DB.dbo.Activation" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="HardwareID" DataSetColumn="HardwareID" />
              <Mapping SourceColumn="SerialNo" DataSetColumn="SerialNo" />
              <Mapping SourceColumn="ActivationID" DataSetColumn="ActivationID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CategoryTableAdapter" GeneratorDataComponentClassName="CategoryTableAdapter" Name="Category" UserDataComponentName="CategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Category" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Category] WHERE (([CategoryName] = @Original_CategoryName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Category] ([CategoryName]) VALUES (@CategoryName);
SELECT CategoryName FROM Category WHERE (CategoryName = @CategoryName)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT CategoryName FROM dbo.Category</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Category] SET [CategoryName] = @CategoryName WHERE (([CategoryName] = @Original_CategoryName));
SELECT CategoryName FROM Category WHERE (CategoryName = @CategoryName)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CompanyTableAdapter" GeneratorDataComponentClassName="CompanyTableAdapter" Name="Company" UserDataComponentName="CompanyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Company" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Company] WHERE (([ID] = @Original_ID) AND ([CompanyName] = @Original_CompanyName) AND ([Address] = @Original_Address) AND ([ContactNo] = @Original_ContactNo) AND ([EmailID] = @Original_EmailID) AND ((@IsNull_TIN = 1 AND [TIN] IS NULL) OR ([TIN] = @Original_TIN)) AND ((@IsNull_STNo = 1 AND [STNo] IS NULL) OR ([STNo] = @Original_STNo)) AND ((@IsNull_CIN = 1 AND [CIN] IS NULL) OR ([CIN] = @Original_CIN)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_TIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_STNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Company] ([CompanyName], [Address], [ContactNo], [EmailID], [Logo], [TIN], [STNo], [CIN]) VALUES (@CompanyName, @Address, @ContactNo, @EmailID, @Logo, @TIN, @STNo, @CIN);
SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM Company WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Logo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Logo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM dbo.Company</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Company] SET [CompanyName] = @CompanyName, [Address] = @Address, [ContactNo] = @ContactNo, [EmailID] = @EmailID, [Logo] = @Logo, [TIN] = @TIN, [STNo] = @STNo, [CIN] = @CIN WHERE (([ID] = @Original_ID) AND ([CompanyName] = @Original_CompanyName) AND ([Address] = @Original_Address) AND ([ContactNo] = @Original_ContactNo) AND ([EmailID] = @Original_EmailID) AND ((@IsNull_TIN = 1 AND [TIN] IS NULL) OR ([TIN] = @Original_TIN)) AND ((@IsNull_STNo = 1 AND [STNo] IS NULL) OR ([STNo] = @Original_STNo)) AND ((@IsNull_CIN = 1 AND [CIN] IS NULL) OR ([CIN] = @Original_CIN)));
SELECT ID, CompanyName, Address, ContactNo, EmailID, Logo, TIN, STNo, CIN FROM Company WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Logo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Logo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CompanyName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CompanyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_TIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_TIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="TIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_STNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_STNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="STNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CIN" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CIN" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CIN" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="INV_DB.dbo.Company" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="CompanyName" DataSetColumn="CompanyName" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="Logo" DataSetColumn="Logo" />
              <Mapping SourceColumn="TIN" DataSetColumn="TIN" />
              <Mapping SourceColumn="STNo" DataSetColumn="STNo" />
              <Mapping SourceColumn="CIN" DataSetColumn="CIN" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Company_ContactsTableAdapter" GeneratorDataComponentClassName="Company_ContactsTableAdapter" Name="Company_Contacts" UserDataComponentName="Company_ContactsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Company_Contacts" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Company_Contacts] WHERE (([Id] = @Original_Id) AND ([ContactPerson] = @Original_ContactPerson) AND ([ContactNo] = @Original_ContactNo))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactPerson" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactPerson" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Company_Contacts] ([ContactPerson], [ContactNo]) VALUES (@ContactPerson, @ContactNo);
SELECT Id, ContactPerson, ContactNo FROM Company_Contacts WHERE (Id = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactPerson" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactPerson" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Id, ContactPerson, ContactNo FROM dbo.Company_Contacts</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Company_Contacts] SET [ContactPerson] = @ContactPerson, [ContactNo] = @ContactNo WHERE (([Id] = @Original_Id) AND ([ContactPerson] = @Original_ContactPerson) AND ([ContactNo] = @Original_ContactNo));
SELECT Id, ContactPerson, ContactNo FROM Company_Contacts WHERE (Id = @Id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactPerson" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactPerson" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactPerson" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactPerson" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Id" ColumnName="Id" DataSourceName="INV_DB.dbo.Company_Contacts" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Id" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Id" DataSetColumn="Id" />
              <Mapping SourceColumn="ContactPerson" DataSetColumn="ContactPerson" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CustomerTableAdapter" GeneratorDataComponentClassName="CustomerTableAdapter" Name="Customer" UserDataComponentName="CustomerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Customer" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Customer] WHERE (([ID] = @Original_ID) AND ((@IsNull_CustomerID = 1 AND [CustomerID] IS NULL) OR ([CustomerID] = @Original_CustomerID)) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ((@IsNull_Gender = 1 AND [Gender] IS NULL) OR ([Gender] = @Original_Gender)) AND ((@IsNull_Address = 1 AND [Address] IS NULL) OR ([Address] = @Original_Address)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_State = 1 AND [State] IS NULL) OR ([State] = @Original_State)) AND ((@IsNull_ZipCode = 1 AND [ZipCode] IS NULL) OR ([ZipCode] = @Original_ZipCode)) AND ((@IsNull_ContactNo = 1 AND [ContactNo] IS NULL) OR ([ContactNo] = @Original_ContactNo)) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)) AND ((@IsNull_CustomerType = 1 AND [CustomerType] IS NULL) OR ([CustomerType] = @Original_CustomerType)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Gender" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Gender" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Address" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_State" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ZipCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CustomerType" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CustomerType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Customer] ([ID], [CustomerID], [Name], [Gender], [Address], [City], [State], [ZipCode], [ContactNo], [EmailID], [Remarks], [Photo], [CustomerType]) VALUES (@ID, @CustomerID, @Name, @Gender, @Address, @City, @State, @ZipCode, @ContactNo, @EmailID, @Remarks, @Photo, @CustomerType);
SELECT ID, CustomerID, Name, Gender, Address, City, State, ZipCode, ContactNo, EmailID, Remarks, Photo, CustomerType FROM Customer WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Gender" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Photo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CustomerType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, CustomerID, Name, Gender, Address, City, State, ZipCode, ContactNo, EmailID, Remarks, Photo, CustomerType FROM dbo.Customer</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Customer] SET [ID] = @ID, [CustomerID] = @CustomerID, [Name] = @Name, [Gender] = @Gender, [Address] = @Address, [City] = @City, [State] = @State, [ZipCode] = @ZipCode, [ContactNo] = @ContactNo, [EmailID] = @EmailID, [Remarks] = @Remarks, [Photo] = @Photo, [CustomerType] = @CustomerType WHERE (([ID] = @Original_ID) AND ((@IsNull_CustomerID = 1 AND [CustomerID] IS NULL) OR ([CustomerID] = @Original_CustomerID)) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ((@IsNull_Gender = 1 AND [Gender] IS NULL) OR ([Gender] = @Original_Gender)) AND ((@IsNull_Address = 1 AND [Address] IS NULL) OR ([Address] = @Original_Address)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_State = 1 AND [State] IS NULL) OR ([State] = @Original_State)) AND ((@IsNull_ZipCode = 1 AND [ZipCode] IS NULL) OR ([ZipCode] = @Original_ZipCode)) AND ((@IsNull_ContactNo = 1 AND [ContactNo] IS NULL) OR ([ContactNo] = @Original_ContactNo)) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)) AND ((@IsNull_CustomerType = 1 AND [CustomerType] IS NULL) OR ([CustomerType] = @Original_CustomerType)));
SELECT ID, CustomerID, Name, Gender, Address, City, State, ZipCode, ContactNo, EmailID, Remarks, Photo, CustomerType FROM Customer WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Gender" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Photo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@CustomerType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Gender" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Gender" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Gender" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Address" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_State" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ZipCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_CustomerType" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_CustomerType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="CustomerType" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="CustomerID" DataSetColumn="CustomerID" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Gender" DataSetColumn="Gender" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="State" DataSetColumn="State" />
              <Mapping SourceColumn="ZipCode" DataSetColumn="ZipCode" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
              <Mapping SourceColumn="Photo" DataSetColumn="Photo" />
              <Mapping SourceColumn="CustomerType" DataSetColumn="CustomerType" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Invoice_PaymentTableAdapter" GeneratorDataComponentClassName="Invoice_PaymentTableAdapter" Name="Invoice_Payment" UserDataComponentName="Invoice_PaymentTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Invoice_Payment" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Invoice_Payment] WHERE (([IP_ID] = @Original_IP_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([PaymentDate] = @Original_PaymentDate) AND ([TotalPaid] = @Original_TotalPaid) AND ([PaymentMode] = @Original_PaymentMode))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Invoice_Payment] ([InvoiceID], [PaymentDate], [TotalPaid], [PaymentMode]) VALUES (@InvoiceID, @PaymentDate, @TotalPaid, @PaymentMode);
SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM Invoice_Payment WHERE (IP_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM dbo.Invoice_Payment</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Invoice_Payment] SET [InvoiceID] = @InvoiceID, [PaymentDate] = @PaymentDate, [TotalPaid] = @TotalPaid, [PaymentMode] = @PaymentMode WHERE (([IP_ID] = @Original_IP_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([PaymentDate] = @Original_PaymentDate) AND ([TotalPaid] = @Original_TotalPaid) AND ([PaymentMode] = @Original_PaymentMode));
SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM Invoice_Payment WHERE (IP_ID = @IP_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="IP_ID" ColumnName="IP_ID" DataSourceName="INV_DB.dbo.Invoice_Payment" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IP_ID" DataSetColumn="IP_ID" />
              <Mapping SourceColumn="InvoiceID" DataSetColumn="InvoiceID" />
              <Mapping SourceColumn="PaymentDate" DataSetColumn="PaymentDate" />
              <Mapping SourceColumn="TotalPaid" DataSetColumn="TotalPaid" />
              <Mapping SourceColumn="PaymentMode" DataSetColumn="PaymentMode" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Invoice_ProductTableAdapter" GeneratorDataComponentClassName="Invoice_ProductTableAdapter" Name="Invoice_Product" UserDataComponentName="Invoice_ProductTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Invoice_Product" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Invoice_Product] WHERE (([IPo_ID] = @Original_IPo_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([ProductID] = @Original_ProductID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Margin] = @Original_Margin) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount) AND ((@IsNull_Barcode = 1 AND [Barcode] IS NULL) OR ([Barcode] = @Original_Barcode)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IPo_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IPo_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Margin" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Barcode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Barcode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Invoice_Product] ([InvoiceID], [ProductID], [CostPrice], [SellingPrice], [Margin], [Qty], [Amount], [DiscountPer], [Discount], [VATPer], [VAT], [TotalAmount], [Barcode]) VALUES (@InvoiceID, @ProductID, @CostPrice, @SellingPrice, @Margin, @Qty, @Amount, @DiscountPer, @Discount, @VATPer, @VAT, @TotalAmount, @Barcode);
SELECT IPo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount, Barcode FROM Invoice_Product WHERE (IPo_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Margin" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Barcode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT IPo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount,Barcode FROM dbo.Invoice_Product</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Invoice_Product] SET [InvoiceID] = @InvoiceID, [ProductID] = @ProductID, [CostPrice] = @CostPrice, [SellingPrice] = @SellingPrice, [Margin] = @Margin, [Qty] = @Qty, [Amount] = @Amount, [DiscountPer] = @DiscountPer, [Discount] = @Discount, [VATPer] = @VATPer, [VAT] = @VAT, [TotalAmount] = @TotalAmount, [Barcode] = @Barcode WHERE (([IPo_ID] = @Original_IPo_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([ProductID] = @Original_ProductID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Margin] = @Original_Margin) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount) AND ((@IsNull_Barcode = 1 AND [Barcode] IS NULL) OR ([Barcode] = @Original_Barcode)));
SELECT IPo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount, Barcode FROM Invoice_Product WHERE (IPo_ID = @IPo_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Margin" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Barcode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IPo_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IPo_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Margin" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Barcode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Barcode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Barcode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="IPo_ID" ColumnName="IPo_ID" DataSourceName="INV_DB.dbo.Invoice_Product" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@IPo_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="IPo_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IPo_ID" DataSetColumn="IPo_ID" />
              <Mapping SourceColumn="InvoiceID" DataSetColumn="InvoiceID" />
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="CostPrice" DataSetColumn="CostPrice" />
              <Mapping SourceColumn="SellingPrice" DataSetColumn="SellingPrice" />
              <Mapping SourceColumn="Margin" DataSetColumn="Margin" />
              <Mapping SourceColumn="Qty" DataSetColumn="Qty" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="DiscountPer" DataSetColumn="DiscountPer" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="VATPer" DataSetColumn="VATPer" />
              <Mapping SourceColumn="VAT" DataSetColumn="VAT" />
              <Mapping SourceColumn="TotalAmount" DataSetColumn="TotalAmount" />
              <Mapping SourceColumn="Barcode" DataSetColumn="Barcode" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Invoice1_PaymentTableAdapter" GeneratorDataComponentClassName="Invoice1_PaymentTableAdapter" Name="Invoice1_Payment" UserDataComponentName="Invoice1_PaymentTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Invoice1_Payment" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Invoice1_Payment] WHERE (([IP_ID] = @Original_IP_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([PaymentDate] = @Original_PaymentDate) AND ([TotalPaid] = @Original_TotalPaid) AND ([PaymentMode] = @Original_PaymentMode))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Invoice1_Payment] ([InvoiceID], [PaymentDate], [TotalPaid], [PaymentMode]) VALUES (@InvoiceID, @PaymentDate, @TotalPaid, @PaymentMode);
SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM Invoice1_Payment WHERE (IP_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM dbo.Invoice1_Payment</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Invoice1_Payment] SET [InvoiceID] = @InvoiceID, [PaymentDate] = @PaymentDate, [TotalPaid] = @TotalPaid, [PaymentMode] = @PaymentMode WHERE (([IP_ID] = @Original_IP_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([PaymentDate] = @Original_PaymentDate) AND ([TotalPaid] = @Original_TotalPaid) AND ([PaymentMode] = @Original_PaymentMode));
SELECT IP_ID, InvoiceID, PaymentDate, TotalPaid, PaymentMode FROM Invoice1_Payment WHERE (IP_ID = @IP_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_PaymentDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PaymentDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PaymentMode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PaymentMode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="IP_ID" ColumnName="IP_ID" DataSourceName="INV_DB.dbo.Invoice1_Payment" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@IP_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="IP_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IP_ID" DataSetColumn="IP_ID" />
              <Mapping SourceColumn="InvoiceID" DataSetColumn="InvoiceID" />
              <Mapping SourceColumn="PaymentDate" DataSetColumn="PaymentDate" />
              <Mapping SourceColumn="TotalPaid" DataSetColumn="TotalPaid" />
              <Mapping SourceColumn="PaymentMode" DataSetColumn="PaymentMode" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Invoice1_ProductTableAdapter" GeneratorDataComponentClassName="Invoice1_ProductTableAdapter" Name="Invoice1_Product" UserDataComponentName="Invoice1_ProductTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Invoice1_Product" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Invoice1_Product] WHERE (([Ipo_ID] = @Original_Ipo_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([ProductID] = @Original_ProductID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Margin] = @Original_Margin) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Ipo_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Ipo_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Margin" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Invoice1_Product] ([InvoiceID], [ProductID], [CostPrice], [SellingPrice], [Margin], [Qty], [Amount], [DiscountPer], [Discount], [VATPer], [VAT], [TotalAmount]) VALUES (@InvoiceID, @ProductID, @CostPrice, @SellingPrice, @Margin, @Qty, @Amount, @DiscountPer, @Discount, @VATPer, @VAT, @TotalAmount);
SELECT Ipo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM Invoice1_Product WHERE (Ipo_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Margin" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Ipo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM dbo.Invoice1_Product</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Invoice1_Product] SET [InvoiceID] = @InvoiceID, [ProductID] = @ProductID, [CostPrice] = @CostPrice, [SellingPrice] = @SellingPrice, [Margin] = @Margin, [Qty] = @Qty, [Amount] = @Amount, [DiscountPer] = @DiscountPer, [Discount] = @Discount, [VATPer] = @VATPer, [VAT] = @VAT, [TotalAmount] = @TotalAmount WHERE (([Ipo_ID] = @Original_Ipo_ID) AND ([InvoiceID] = @Original_InvoiceID) AND ([ProductID] = @Original_ProductID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Margin] = @Original_Margin) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount));
SELECT Ipo_ID, InvoiceID, ProductID, CostPrice, SellingPrice, Margin, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM Invoice1_Product WHERE (Ipo_ID = @Ipo_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Margin" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Ipo_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Ipo_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InvoiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InvoiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Margin" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Margin" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Ipo_ID" ColumnName="Ipo_ID" DataSourceName="INV_DB.dbo.Invoice1_Product" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Ipo_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Ipo_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Ipo_ID" DataSetColumn="Ipo_ID" />
              <Mapping SourceColumn="InvoiceID" DataSetColumn="InvoiceID" />
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="CostPrice" DataSetColumn="CostPrice" />
              <Mapping SourceColumn="SellingPrice" DataSetColumn="SellingPrice" />
              <Mapping SourceColumn="Margin" DataSetColumn="Margin" />
              <Mapping SourceColumn="Qty" DataSetColumn="Qty" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="DiscountPer" DataSetColumn="DiscountPer" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="VATPer" DataSetColumn="VATPer" />
              <Mapping SourceColumn="VAT" DataSetColumn="VAT" />
              <Mapping SourceColumn="TotalAmount" DataSetColumn="TotalAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="InvoiceInfoTableAdapter" GeneratorDataComponentClassName="InvoiceInfoTableAdapter" Name="InvoiceInfo" UserDataComponentName="InvoiceInfoTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.InvoiceInfo" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [InvoiceInfo] WHERE (([Inv_ID] = @Original_Inv_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([InvoiceDate] = @Original_InvoiceDate) AND ([CustomerID] = @Original_CustomerID) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPaid] = @Original_TotalPaid) AND ([Balance] = @Original_Balance))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [InvoiceInfo] ([Inv_ID], [InvoiceNo], [InvoiceDate], [CustomerID], [GrandTotal], [TotalPaid], [Balance], [Remarks]) VALUES (@Inv_ID, @InvoiceNo, @InvoiceDate, @CustomerID, @GrandTotal, @TotalPaid, @Balance, @Remarks);
SELECT Inv_ID, InvoiceNo, InvoiceDate, CustomerID, GrandTotal, TotalPaid, Balance, Remarks FROM InvoiceInfo WHERE (Inv_ID = @Inv_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        Inv_ID, InvoiceNo, InvoiceDate, CustomerID, GrandTotal, TotalPaid, Balance, Remarks
FROM            InvoiceInfo</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [InvoiceInfo] SET [Inv_ID] = @Inv_ID, [InvoiceNo] = @InvoiceNo, [InvoiceDate] = @InvoiceDate, [CustomerID] = @CustomerID, [GrandTotal] = @GrandTotal, [TotalPaid] = @TotalPaid, [Balance] = @Balance, [Remarks] = @Remarks WHERE (([Inv_ID] = @Original_Inv_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([InvoiceDate] = @Original_InvoiceDate) AND ([CustomerID] = @Original_CustomerID) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPaid] = @Original_TotalPaid) AND ([Balance] = @Original_Balance));
SELECT Inv_ID, InvoiceNo, InvoiceDate, CustomerID, GrandTotal, TotalPaid, Balance, Remarks FROM InvoiceInfo WHERE (Inv_ID = @Inv_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Inv_ID" DataSetColumn="Inv_ID" />
              <Mapping SourceColumn="InvoiceNo" DataSetColumn="InvoiceNo" />
              <Mapping SourceColumn="InvoiceDate" DataSetColumn="InvoiceDate" />
              <Mapping SourceColumn="CustomerID" DataSetColumn="CustomerID" />
              <Mapping SourceColumn="GrandTotal" DataSetColumn="GrandTotal" />
              <Mapping SourceColumn="TotalPaid" DataSetColumn="TotalPaid" />
              <Mapping SourceColumn="Balance" DataSetColumn="Balance" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="InvoiceInfo1TableAdapter" GeneratorDataComponentClassName="InvoiceInfo1TableAdapter" Name="InvoiceInfo1" UserDataComponentName="InvoiceInfo1TableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.InvoiceInfo1" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[InvoiceInfo1] WHERE (([Inv_ID] = @Original_Inv_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([InvoiceDate] = @Original_InvoiceDate) AND ([ServiceID] = @Original_ServiceID) AND ([RepairCharges] = @Original_RepairCharges) AND ([Upfront] = @Original_Upfront) AND ([ProductCharges] = @Original_ProductCharges) AND ([ServiceTaxPer] = @Original_ServiceTaxPer) AND ([ServiceTax] = @Original_ServiceTax) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPaid] = @Original_TotalPaid) AND ([Balance] = @Original_Balance))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ServiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_RepairCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RepairCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Upfront" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Upfront" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ProductCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ProductCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ServiceTaxPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTaxPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ServiceTax" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTax" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[InvoiceInfo1] ([Inv_ID], [InvoiceNo], [InvoiceDate], [ServiceID], [RepairCharges], [Upfront], [ProductCharges], [ServiceTaxPer], [ServiceTax], [GrandTotal], [TotalPaid], [Balance], [Remarks]) VALUES (@Inv_ID, @InvoiceNo, @InvoiceDate, @ServiceID, @RepairCharges, @Upfront, @ProductCharges, @ServiceTaxPer, @ServiceTax, @GrandTotal, @TotalPaid, @Balance, @Remarks);
SELECT Inv_ID, InvoiceNo, InvoiceDate, ServiceID, RepairCharges, Upfront, ProductCharges, ServiceTaxPer, ServiceTax, GrandTotal, TotalPaid, Balance, Remarks FROM InvoiceInfo1 WHERE (Inv_ID = @Inv_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ServiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@RepairCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RepairCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Upfront" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Upfront" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ProductCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ProductCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ServiceTaxPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTaxPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ServiceTax" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTax" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Inv_ID, InvoiceNo, InvoiceDate, ServiceID, RepairCharges, Upfront, ProductCharges, ServiceTaxPer, ServiceTax, GrandTotal, TotalPaid, Balance, Remarks FROM dbo.InvoiceInfo1</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[InvoiceInfo1] SET [Inv_ID] = @Inv_ID, [InvoiceNo] = @InvoiceNo, [InvoiceDate] = @InvoiceDate, [ServiceID] = @ServiceID, [RepairCharges] = @RepairCharges, [Upfront] = @Upfront, [ProductCharges] = @ProductCharges, [ServiceTaxPer] = @ServiceTaxPer, [ServiceTax] = @ServiceTax, [GrandTotal] = @GrandTotal, [TotalPaid] = @TotalPaid, [Balance] = @Balance, [Remarks] = @Remarks WHERE (([Inv_ID] = @Original_Inv_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([InvoiceDate] = @Original_InvoiceDate) AND ([ServiceID] = @Original_ServiceID) AND ([RepairCharges] = @Original_RepairCharges) AND ([Upfront] = @Original_Upfront) AND ([ProductCharges] = @Original_ProductCharges) AND ([ServiceTaxPer] = @Original_ServiceTaxPer) AND ([ServiceTax] = @Original_ServiceTax) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPaid] = @Original_TotalPaid) AND ([Balance] = @Original_Balance));
SELECT Inv_ID, InvoiceNo, InvoiceDate, ServiceID, RepairCharges, Upfront, ProductCharges, ServiceTaxPer, ServiceTax, GrandTotal, TotalPaid, Balance, Remarks FROM InvoiceInfo1 WHERE (Inv_ID = @Inv_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ServiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@RepairCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RepairCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Upfront" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Upfront" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ProductCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ProductCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ServiceTaxPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTaxPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ServiceTax" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTax" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Inv_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Inv_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_InvoiceDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InvoiceDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ServiceID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_RepairCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RepairCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Upfront" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Upfront" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ProductCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ProductCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ServiceTaxPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTaxPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ServiceTax" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ServiceTax" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPaid" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPaid" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Balance" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Balance" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Inv_ID" DataSetColumn="Inv_ID" />
              <Mapping SourceColumn="InvoiceNo" DataSetColumn="InvoiceNo" />
              <Mapping SourceColumn="InvoiceDate" DataSetColumn="InvoiceDate" />
              <Mapping SourceColumn="ServiceID" DataSetColumn="ServiceID" />
              <Mapping SourceColumn="RepairCharges" DataSetColumn="RepairCharges" />
              <Mapping SourceColumn="Upfront" DataSetColumn="Upfront" />
              <Mapping SourceColumn="ProductCharges" DataSetColumn="ProductCharges" />
              <Mapping SourceColumn="ServiceTaxPer" DataSetColumn="ServiceTaxPer" />
              <Mapping SourceColumn="ServiceTax" DataSetColumn="ServiceTax" />
              <Mapping SourceColumn="GrandTotal" DataSetColumn="GrandTotal" />
              <Mapping SourceColumn="TotalPaid" DataSetColumn="TotalPaid" />
              <Mapping SourceColumn="Balance" DataSetColumn="Balance" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="LogsTableAdapter" GeneratorDataComponentClassName="LogsTableAdapter" Name="Logs" UserDataComponentName="LogsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Logs" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Logs] WHERE (([ID] = @Original_ID) AND ([UserID] = @Original_UserID) AND ([Date] = @Original_Date))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Logs] ([UserID], [Operation], [Date]) VALUES (@UserID, @Operation, @Date);
SELECT ID, UserID, Operation, Date FROM Logs WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Operation" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Operation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, UserID, Operation, Date FROM dbo.Logs</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Logs] SET [UserID] = @UserID, [Operation] = @Operation, [Date] = @Date WHERE (([ID] = @Original_ID) AND ([UserID] = @Original_UserID) AND ([Date] = @Original_Date));
SELECT ID, UserID, Operation, Date FROM Logs WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Operation" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Operation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="INV_DB.dbo.Logs" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="UserID" DataSetColumn="UserID" />
              <Mapping SourceColumn="Operation" DataSetColumn="Operation" />
              <Mapping SourceColumn="Date" DataSetColumn="Date" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ProductTableAdapter" GeneratorDataComponentClassName="ProductTableAdapter" Name="Product" UserDataComponentName="ProductTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Product" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Product] WHERE (([PID] = @Original_PID) AND ([ProductCode] = @Original_ProductCode) AND ([ProductName] = @Original_ProductName) AND ([SubCategoryID] = @Original_SubCategoryID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Discount] = @Original_Discount) AND ([VAT] = @Original_VAT) AND ([ReorderPoint] = @Original_ReorderPoint) AND ([OpeningStock] = @Original_OpeningStock))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_PID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ProductCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ProductName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SubCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SubCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ReorderPoint" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ReorderPoint" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_OpeningStock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="OpeningStock" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Product] ([PID], [ProductCode], [ProductName], [SubCategoryID], [Description], [CostPrice], [SellingPrice], [Discount], [VAT], [ReorderPoint], [OpeningStock]) VALUES (@PID, @ProductCode, @ProductName, @SubCategoryID, @Description, @CostPrice, @SellingPrice, @Discount, @VAT, @ReorderPoint, @OpeningStock);
SELECT PID, ProductCode, ProductName, SubCategoryID, Description, CostPrice, SellingPrice, Discount, VAT, ReorderPoint, OpeningStock FROM Product WHERE (PID = @PID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ProductCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ProductName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@SubCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SubCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ReorderPoint" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ReorderPoint" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@OpeningStock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="OpeningStock" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT PID, ProductCode, ProductName, SubCategoryID, Description, CostPrice, SellingPrice, Discount, VAT, ReorderPoint, OpeningStock FROM dbo.Product</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Product] SET [PID] = @PID, [ProductCode] = @ProductCode, [ProductName] = @ProductName, [SubCategoryID] = @SubCategoryID, [Description] = @Description, [CostPrice] = @CostPrice, [SellingPrice] = @SellingPrice, [Discount] = @Discount, [VAT] = @VAT, [ReorderPoint] = @ReorderPoint, [OpeningStock] = @OpeningStock WHERE (([PID] = @Original_PID) AND ([ProductCode] = @Original_ProductCode) AND ([ProductName] = @Original_ProductName) AND ([SubCategoryID] = @Original_SubCategoryID) AND ([CostPrice] = @Original_CostPrice) AND ([SellingPrice] = @Original_SellingPrice) AND ([Discount] = @Original_Discount) AND ([VAT] = @Original_VAT) AND ([ReorderPoint] = @Original_ReorderPoint) AND ([OpeningStock] = @Original_OpeningStock));
SELECT PID, ProductCode, ProductName, SubCategoryID, Description, CostPrice, SellingPrice, Discount, VAT, ReorderPoint, OpeningStock FROM Product WHERE (PID = @PID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ProductCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ProductName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@SubCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SubCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Description" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Description" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ReorderPoint" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ReorderPoint" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@OpeningStock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="OpeningStock" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_PID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ProductCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ProductName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SubCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SubCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SellingPrice" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="SellingPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="3" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ReorderPoint" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ReorderPoint" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_OpeningStock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="OpeningStock" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="PID" DataSetColumn="PID" />
              <Mapping SourceColumn="ProductCode" DataSetColumn="ProductCode" />
              <Mapping SourceColumn="ProductName" DataSetColumn="ProductName" />
              <Mapping SourceColumn="SubCategoryID" DataSetColumn="SubCategoryID" />
              <Mapping SourceColumn="Description" DataSetColumn="Description" />
              <Mapping SourceColumn="CostPrice" DataSetColumn="CostPrice" />
              <Mapping SourceColumn="SellingPrice" DataSetColumn="SellingPrice" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="VAT" DataSetColumn="VAT" />
              <Mapping SourceColumn="ReorderPoint" DataSetColumn="ReorderPoint" />
              <Mapping SourceColumn="OpeningStock" DataSetColumn="OpeningStock" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Product_JoinTableAdapter" GeneratorDataComponentClassName="Product_JoinTableAdapter" Name="Product_Join" UserDataComponentName="Product_JoinTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Product_Join" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Product_Join] WHERE (([Id] = @Original_Id) AND ([ProductID] = @Original_ProductID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Product_Join] ([ProductID], [Photo]) VALUES (@ProductID, @Photo);
SELECT Id, ProductID, Photo FROM Product_Join WHERE (Id = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Photo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Id, ProductID, Photo FROM dbo.Product_Join</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Product_Join] SET [ProductID] = @ProductID, [Photo] = @Photo WHERE (([Id] = @Original_Id) AND ([ProductID] = @Original_ProductID));
SELECT Id, ProductID, Photo FROM Product_Join WHERE (Id = @Id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Binary" Direction="Input" ParameterName="@Photo" Precision="0" ProviderType="Image" Scale="0" Size="0" SourceColumn="Photo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Id" ColumnName="Id" DataSourceName="INV_DB.dbo.Product_Join" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Id" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Id" DataSetColumn="Id" />
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="Photo" DataSetColumn="Photo" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="QuotationTableAdapter" GeneratorDataComponentClassName="QuotationTableAdapter" Name="Quotation" UserDataComponentName="QuotationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Quotation" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Quotation] WHERE (([Q_ID] = @Original_Q_ID) AND ([QuotationNo] = @Original_QuotationNo) AND ([Date] = @Original_Date) AND ([CustomerID] = @Original_CustomerID) AND ([GrandTotal] = @Original_GrandTotal))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Q_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Q_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_QuotationNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="QuotationNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Quotation] ([Q_ID], [QuotationNo], [Date], [CustomerID], [GrandTotal], [Remarks]) VALUES (@Q_ID, @QuotationNo, @Date, @CustomerID, @GrandTotal, @Remarks);
SELECT Q_ID, QuotationNo, Date, CustomerID, GrandTotal, Remarks FROM Quotation WHERE (Q_ID = @Q_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Q_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Q_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@QuotationNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="QuotationNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Q_ID, QuotationNo, Date, CustomerID, GrandTotal, Remarks FROM dbo.Quotation</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Quotation] SET [Q_ID] = @Q_ID, [QuotationNo] = @QuotationNo, [Date] = @Date, [CustomerID] = @CustomerID, [GrandTotal] = @GrandTotal, [Remarks] = @Remarks WHERE (([Q_ID] = @Original_Q_ID) AND ([QuotationNo] = @Original_QuotationNo) AND ([Date] = @Original_Date) AND ([CustomerID] = @Original_CustomerID) AND ([GrandTotal] = @Original_GrandTotal));
SELECT Q_ID, QuotationNo, Date, CustomerID, GrandTotal, Remarks FROM Quotation WHERE (Q_ID = @Q_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Q_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Q_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@QuotationNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="QuotationNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Q_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Q_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_QuotationNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="QuotationNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Q_ID" DataSetColumn="Q_ID" />
              <Mapping SourceColumn="QuotationNo" DataSetColumn="QuotationNo" />
              <Mapping SourceColumn="Date" DataSetColumn="Date" />
              <Mapping SourceColumn="CustomerID" DataSetColumn="CustomerID" />
              <Mapping SourceColumn="GrandTotal" DataSetColumn="GrandTotal" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Quotation_JoinTableAdapter" GeneratorDataComponentClassName="Quotation_JoinTableAdapter" Name="Quotation_Join" UserDataComponentName="Quotation_JoinTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Quotation_Join" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Quotation_Join] WHERE (([QJ_ID] = @Original_QJ_ID) AND ([QuotationID] = @Original_QuotationID) AND ([ProductID] = @Original_ProductID) AND ([Cost] = @Original_Cost) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_QJ_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QJ_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_QuotationID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QuotationID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Cost" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Cost" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Quotation_Join] ([QuotationID], [ProductID], [Cost], [Qty], [Amount], [DiscountPer], [Discount], [VATPer], [VAT], [TotalAmount]) VALUES (@QuotationID, @ProductID, @Cost, @Qty, @Amount, @DiscountPer, @Discount, @VATPer, @VAT, @TotalAmount);
SELECT QJ_ID, QuotationID, ProductID, Cost, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM Quotation_Join WHERE (QJ_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@QuotationID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QuotationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Cost" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Cost" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT QJ_ID, QuotationID, ProductID, Cost, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM dbo.Quotation_Join</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Quotation_Join] SET [QuotationID] = @QuotationID, [ProductID] = @ProductID, [Cost] = @Cost, [Qty] = @Qty, [Amount] = @Amount, [DiscountPer] = @DiscountPer, [Discount] = @Discount, [VATPer] = @VATPer, [VAT] = @VAT, [TotalAmount] = @TotalAmount WHERE (([QJ_ID] = @Original_QJ_ID) AND ([QuotationID] = @Original_QuotationID) AND ([ProductID] = @Original_ProductID) AND ([Cost] = @Original_Cost) AND ([Qty] = @Original_Qty) AND ([Amount] = @Original_Amount) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([VATPer] = @Original_VATPer) AND ([VAT] = @Original_VAT) AND ([TotalAmount] = @Original_TotalAmount));
SELECT QJ_ID, QuotationID, ProductID, Cost, Qty, Amount, DiscountPer, Discount, VATPer, VAT, TotalAmount FROM Quotation_Join WHERE (QJ_ID = @QJ_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@QuotationID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QuotationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Cost" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Cost" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_QJ_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QJ_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_QuotationID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="QuotationID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Cost" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Cost" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VATPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VATPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_VAT" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="VAT" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="QJ_ID" ColumnName="QJ_ID" DataSourceName="INV_DB.dbo.Quotation_Join" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@QJ_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="QJ_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="QJ_ID" DataSetColumn="QJ_ID" />
              <Mapping SourceColumn="QuotationID" DataSetColumn="QuotationID" />
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="Cost" DataSetColumn="Cost" />
              <Mapping SourceColumn="Qty" DataSetColumn="Qty" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="DiscountPer" DataSetColumn="DiscountPer" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="VATPer" DataSetColumn="VATPer" />
              <Mapping SourceColumn="VAT" DataSetColumn="VAT" />
              <Mapping SourceColumn="TotalAmount" DataSetColumn="TotalAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="RegistrationTableAdapter" GeneratorDataComponentClassName="RegistrationTableAdapter" Name="Registration" UserDataComponentName="RegistrationTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Registration" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Registration] WHERE (([UserID] = @Original_UserID) AND ([UserType] = @Original_UserType) AND ([Password] = @Original_Password) AND ([Name] = @Original_Name) AND ([ContactNo] = @Original_ContactNo) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)) AND ((@IsNull_JoiningDate = 1 AND [JoiningDate] IS NULL) OR ([JoiningDate] = @Original_JoiningDate)) AND ((@IsNull_Active = 1 AND [Active] IS NULL) OR ([Active] = @Original_Active)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Password" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_JoiningDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_JoiningDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Active" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Active" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Registration] ([UserID], [UserType], [Password], [Name], [ContactNo], [EmailID], [JoiningDate], [Active]) VALUES (@UserID, @UserType, @Password, @Name, @ContactNo, @EmailID, @JoiningDate, @Active);
SELECT UserID, UserType, Password, Name, ContactNo, EmailID, JoiningDate, Active FROM Registration WHERE (UserID = @UserID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Password" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@JoiningDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Active" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT UserID, UserType, Password, Name, ContactNo, EmailID, JoiningDate, Active FROM dbo.Registration</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Registration] SET [UserID] = @UserID, [UserType] = @UserType, [Password] = @Password, [Name] = @Name, [ContactNo] = @ContactNo, [EmailID] = @EmailID, [JoiningDate] = @JoiningDate, [Active] = @Active WHERE (([UserID] = @Original_UserID) AND ([UserType] = @Original_UserType) AND ([Password] = @Original_Password) AND ([Name] = @Original_Name) AND ([ContactNo] = @Original_ContactNo) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)) AND ((@IsNull_JoiningDate = 1 AND [JoiningDate] IS NULL) OR ([JoiningDate] = @Original_JoiningDate)) AND ((@IsNull_Active = 1 AND [Active] IS NULL) OR ([Active] = @Original_Active)));
SELECT UserID, UserType, Password, Name, ContactNo, EmailID, JoiningDate, Active FROM Registration WHERE (UserID = @UserID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@UserType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Password" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@JoiningDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Active" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_UserType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="UserType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Password" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Password" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_JoiningDate" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_JoiningDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="JoiningDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Active" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Active" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Active" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="UserID" DataSetColumn="UserID" />
              <Mapping SourceColumn="UserType" DataSetColumn="UserType" />
              <Mapping SourceColumn="Password" DataSetColumn="Password" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="JoiningDate" DataSetColumn="JoiningDate" />
              <Mapping SourceColumn="Active" DataSetColumn="Active" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ServiceTableAdapter" GeneratorDataComponentClassName="ServiceTableAdapter" Name="Service" UserDataComponentName="ServiceTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Service" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Service] WHERE (([S_ID] = @Original_S_ID) AND ([ServiceCode] = @Original_ServiceCode) AND ([CustomerID] = @Original_CustomerID) AND ((@IsNull_ServiceType = 1 AND [ServiceType] IS NULL) OR ([ServiceType] = @Original_ServiceType)) AND ([ServiceCreationDate] = @Original_ServiceCreationDate) AND ([ChargesQuote] = @Original_ChargesQuote) AND ([AdvanceDeposit] = @Original_AdvanceDeposit) AND ([EstimatedRepairDate] = @Original_EstimatedRepairDate) AND ([Status] = @Original_Status))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_S_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="S_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ServiceCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ServiceType" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ServiceType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_ServiceCreationDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="ServiceCreationDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ChargesQuote" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ChargesQuote" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_AdvanceDeposit" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="AdvanceDeposit" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EstimatedRepairDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EstimatedRepairDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Service] ([S_ID], [ServiceCode], [CustomerID], [ServiceType], [ServiceCreationDate], [ItemDescription], [ProblemDescription], [ChargesQuote], [AdvanceDeposit], [EstimatedRepairDate], [Remarks], [Status]) VALUES (@S_ID, @ServiceCode, @CustomerID, @ServiceType, @ServiceCreationDate, @ItemDescription, @ProblemDescription, @ChargesQuote, @AdvanceDeposit, @EstimatedRepairDate, @Remarks, @Status);
SELECT S_ID, ServiceCode, CustomerID, ServiceType, ServiceCreationDate, ItemDescription, ProblemDescription, ChargesQuote, AdvanceDeposit, EstimatedRepairDate, Remarks, Status FROM Service WHERE (S_ID = @S_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@S_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="S_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ServiceCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ServiceType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@ServiceCreationDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="ServiceCreationDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ItemDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ItemDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ProblemDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProblemDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ChargesQuote" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ChargesQuote" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@AdvanceDeposit" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="AdvanceDeposit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EstimatedRepairDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EstimatedRepairDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT S_ID, ServiceCode, CustomerID, ServiceType, ServiceCreationDate, ItemDescription, ProblemDescription, ChargesQuote, AdvanceDeposit, EstimatedRepairDate, Remarks, Status FROM dbo.Service</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Service] SET [S_ID] = @S_ID, [ServiceCode] = @ServiceCode, [CustomerID] = @CustomerID, [ServiceType] = @ServiceType, [ServiceCreationDate] = @ServiceCreationDate, [ItemDescription] = @ItemDescription, [ProblemDescription] = @ProblemDescription, [ChargesQuote] = @ChargesQuote, [AdvanceDeposit] = @AdvanceDeposit, [EstimatedRepairDate] = @EstimatedRepairDate, [Remarks] = @Remarks, [Status] = @Status WHERE (([S_ID] = @Original_S_ID) AND ([ServiceCode] = @Original_ServiceCode) AND ([CustomerID] = @Original_CustomerID) AND ((@IsNull_ServiceType = 1 AND [ServiceType] IS NULL) OR ([ServiceType] = @Original_ServiceType)) AND ([ServiceCreationDate] = @Original_ServiceCreationDate) AND ([ChargesQuote] = @Original_ChargesQuote) AND ([AdvanceDeposit] = @Original_AdvanceDeposit) AND ([EstimatedRepairDate] = @Original_EstimatedRepairDate) AND ([Status] = @Original_Status));
SELECT S_ID, ServiceCode, CustomerID, ServiceType, ServiceCreationDate, ItemDescription, ProblemDescription, ChargesQuote, AdvanceDeposit, EstimatedRepairDate, Remarks, Status FROM Service WHERE (S_ID = @S_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@S_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="S_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ServiceCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ServiceType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@ServiceCreationDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="ServiceCreationDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ItemDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ItemDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ProblemDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ProblemDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@ChargesQuote" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ChargesQuote" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@AdvanceDeposit" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="AdvanceDeposit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EstimatedRepairDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EstimatedRepairDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Status" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_S_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="S_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ServiceCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CustomerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CustomerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ServiceType" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ServiceType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ServiceType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_ServiceCreationDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="ServiceCreationDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_ChargesQuote" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="ChargesQuote" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_AdvanceDeposit" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="AdvanceDeposit" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EstimatedRepairDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EstimatedRepairDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Status" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Status" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="S_ID" DataSetColumn="S_ID" />
              <Mapping SourceColumn="ServiceCode" DataSetColumn="ServiceCode" />
              <Mapping SourceColumn="CustomerID" DataSetColumn="CustomerID" />
              <Mapping SourceColumn="ServiceType" DataSetColumn="ServiceType" />
              <Mapping SourceColumn="ServiceCreationDate" DataSetColumn="ServiceCreationDate" />
              <Mapping SourceColumn="ItemDescription" DataSetColumn="ItemDescription" />
              <Mapping SourceColumn="ProblemDescription" DataSetColumn="ProblemDescription" />
              <Mapping SourceColumn="ChargesQuote" DataSetColumn="ChargesQuote" />
              <Mapping SourceColumn="AdvanceDeposit" DataSetColumn="AdvanceDeposit" />
              <Mapping SourceColumn="EstimatedRepairDate" DataSetColumn="EstimatedRepairDate" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
              <Mapping SourceColumn="Status" DataSetColumn="Status" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SMSSettingTableAdapter" GeneratorDataComponentClassName="SMSSettingTableAdapter" Name="SMSSetting" UserDataComponentName="SMSSettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.SMSSetting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[SMSSetting] WHERE (([Id] = @Original_Id) AND ([IsDefault] = @Original_IsDefault) AND ([IsEnabled] = @Original_IsEnabled))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_IsDefault" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsDefault" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_IsEnabled" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsEnabled" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[SMSSetting] ([APIUrl], [IsDefault], [IsEnabled]) VALUES (@APIUrl, @IsDefault, @IsEnabled);
SELECT Id, APIUrl, IsDefault, IsEnabled FROM SMSSetting WHERE (Id = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@APIUrl" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="APIUrl" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@IsDefault" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsDefault" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@IsEnabled" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsEnabled" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Id, APIUrl, IsDefault, IsEnabled FROM dbo.SMSSetting</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[SMSSetting] SET [APIUrl] = @APIUrl, [IsDefault] = @IsDefault, [IsEnabled] = @IsEnabled WHERE (([Id] = @Original_Id) AND ([IsDefault] = @Original_IsDefault) AND ([IsEnabled] = @Original_IsEnabled));
SELECT Id, APIUrl, IsDefault, IsEnabled FROM SMSSetting WHERE (Id = @Id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@APIUrl" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="APIUrl" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@IsDefault" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsDefault" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@IsEnabled" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsEnabled" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_IsDefault" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsDefault" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_IsEnabled" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="IsEnabled" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Id" ColumnName="Id" DataSourceName="INV_DB.dbo.SMSSetting" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Id" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Id" DataSetColumn="Id" />
              <Mapping SourceColumn="APIUrl" DataSetColumn="APIUrl" />
              <Mapping SourceColumn="IsDefault" DataSetColumn="IsDefault" />
              <Mapping SourceColumn="IsEnabled" DataSetColumn="IsEnabled" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StockTableAdapter" GeneratorDataComponentClassName="StockTableAdapter" Name="Stock" UserDataComponentName="StockTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Stock" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Stock] WHERE (([ST_ID] = @Original_ST_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([Date] = @Original_Date) AND ([PurchaseType] = @Original_PurchaseType) AND ([SupplierID] = @Original_SupplierID) AND ([SubTotal] = @Original_SubTotal) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([PreviousDue] = @Original_PreviousDue) AND ([FreightCharges] = @Original_FreightCharges) AND ([OtherCharges] = @Original_OtherCharges) AND ([Total] = @Original_Total) AND ([RoundOff] = @Original_RoundOff) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPayment] = @Original_TotalPayment) AND ([PaymentDue] = @Original_PaymentDue))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ST_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ST_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PurchaseType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PurchaseType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SupplierID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SubTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SubTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PreviousDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PreviousDue" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_FreightCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="FreightCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_OtherCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="OtherCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Total" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Total" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_RoundOff" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RoundOff" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPayment" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPayment" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PaymentDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PaymentDue" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Stock] ([ST_ID], [InvoiceNo], [Date], [PurchaseType], [SupplierID], [SubTotal], [DiscountPer], [Discount], [PreviousDue], [FreightCharges], [OtherCharges], [Total], [RoundOff], [GrandTotal], [TotalPayment], [PaymentDue], [Remarks]) VALUES (@ST_ID, @InvoiceNo, @Date, @PurchaseType, @SupplierID, @SubTotal, @DiscountPer, @Discount, @PreviousDue, @FreightCharges, @OtherCharges, @Total, @RoundOff, @GrandTotal, @TotalPayment, @PaymentDue, @Remarks);
SELECT ST_ID, InvoiceNo, Date, PurchaseType, SupplierID, SubTotal, DiscountPer, Discount, PreviousDue, FreightCharges, OtherCharges, Total, RoundOff, GrandTotal, TotalPayment, PaymentDue, Remarks FROM Stock WHERE (ST_ID = @ST_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ST_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ST_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PurchaseType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PurchaseType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@SupplierID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SubTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SubTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PreviousDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PreviousDue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@FreightCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="FreightCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@OtherCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="OtherCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Total" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Total" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@RoundOff" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RoundOff" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPayment" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPayment" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PaymentDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PaymentDue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ST_ID, InvoiceNo, Date, PurchaseType, SupplierID, SubTotal, DiscountPer, Discount, PreviousDue, FreightCharges, OtherCharges, Total, RoundOff, GrandTotal, TotalPayment, PaymentDue, Remarks FROM dbo.Stock</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Stock] SET [ST_ID] = @ST_ID, [InvoiceNo] = @InvoiceNo, [Date] = @Date, [PurchaseType] = @PurchaseType, [SupplierID] = @SupplierID, [SubTotal] = @SubTotal, [DiscountPer] = @DiscountPer, [Discount] = @Discount, [PreviousDue] = @PreviousDue, [FreightCharges] = @FreightCharges, [OtherCharges] = @OtherCharges, [Total] = @Total, [RoundOff] = @RoundOff, [GrandTotal] = @GrandTotal, [TotalPayment] = @TotalPayment, [PaymentDue] = @PaymentDue, [Remarks] = @Remarks WHERE (([ST_ID] = @Original_ST_ID) AND ([InvoiceNo] = @Original_InvoiceNo) AND ([Date] = @Original_Date) AND ([PurchaseType] = @Original_PurchaseType) AND ([SupplierID] = @Original_SupplierID) AND ([SubTotal] = @Original_SubTotal) AND ([DiscountPer] = @Original_DiscountPer) AND ([Discount] = @Original_Discount) AND ([PreviousDue] = @Original_PreviousDue) AND ([FreightCharges] = @Original_FreightCharges) AND ([OtherCharges] = @Original_OtherCharges) AND ([Total] = @Original_Total) AND ([RoundOff] = @Original_RoundOff) AND ([GrandTotal] = @Original_GrandTotal) AND ([TotalPayment] = @Original_TotalPayment) AND ([PaymentDue] = @Original_PaymentDue));
SELECT ST_ID, InvoiceNo, Date, PurchaseType, SupplierID, SubTotal, DiscountPer, Discount, PreviousDue, FreightCharges, OtherCharges, Total, RoundOff, GrandTotal, TotalPayment, PaymentDue, Remarks FROM Stock WHERE (ST_ID = @ST_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ST_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ST_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@PurchaseType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PurchaseType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@SupplierID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@SubTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SubTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PreviousDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PreviousDue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@FreightCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="FreightCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@OtherCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="OtherCharges" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Total" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Total" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@RoundOff" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RoundOff" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalPayment" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPayment" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@PaymentDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PaymentDue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ST_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ST_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_InvoiceNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="InvoiceNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_PurchaseType" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="PurchaseType" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SupplierID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_SubTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="SubTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_DiscountPer" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="DiscountPer" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Discount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PreviousDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PreviousDue" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_FreightCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="FreightCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_OtherCharges" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="OtherCharges" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Total" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Total" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_RoundOff" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="RoundOff" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalPayment" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalPayment" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_PaymentDue" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="PaymentDue" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ST_ID" DataSetColumn="ST_ID" />
              <Mapping SourceColumn="InvoiceNo" DataSetColumn="InvoiceNo" />
              <Mapping SourceColumn="Date" DataSetColumn="Date" />
              <Mapping SourceColumn="PurchaseType" DataSetColumn="PurchaseType" />
              <Mapping SourceColumn="SupplierID" DataSetColumn="SupplierID" />
              <Mapping SourceColumn="SubTotal" DataSetColumn="SubTotal" />
              <Mapping SourceColumn="DiscountPer" DataSetColumn="DiscountPer" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="PreviousDue" DataSetColumn="PreviousDue" />
              <Mapping SourceColumn="FreightCharges" DataSetColumn="FreightCharges" />
              <Mapping SourceColumn="OtherCharges" DataSetColumn="OtherCharges" />
              <Mapping SourceColumn="Total" DataSetColumn="Total" />
              <Mapping SourceColumn="RoundOff" DataSetColumn="RoundOff" />
              <Mapping SourceColumn="GrandTotal" DataSetColumn="GrandTotal" />
              <Mapping SourceColumn="TotalPayment" DataSetColumn="TotalPayment" />
              <Mapping SourceColumn="PaymentDue" DataSetColumn="PaymentDue" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Stock_ProductTableAdapter" GeneratorDataComponentClassName="Stock_ProductTableAdapter" Name="Stock_Product" UserDataComponentName="Stock_ProductTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Stock_Product" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Stock_Product] WHERE (([SP_ID] = @Original_SP_ID) AND ([StockID] = @Original_StockID) AND ([ProductID] = @Original_ProductID) AND ([Qty] = @Original_Qty) AND ([Price] = @Original_Price) AND ([TotalAmount] = @Original_TotalAmount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StockID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StockID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Price" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Price" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Stock_Product] ([StockID], [ProductID], [Qty], [Price], [TotalAmount]) VALUES (@StockID, @ProductID, @Qty, @Price, @TotalAmount);
SELECT SP_ID, StockID, ProductID, Qty, Price, TotalAmount FROM Stock_Product WHERE (SP_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StockID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StockID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Price" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Price" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT SP_ID, StockID, ProductID, Qty, Price, TotalAmount FROM dbo.Stock_Product</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Stock_Product] SET [StockID] = @StockID, [ProductID] = @ProductID, [Qty] = @Qty, [Price] = @Price, [TotalAmount] = @TotalAmount WHERE (([SP_ID] = @Original_SP_ID) AND ([StockID] = @Original_StockID) AND ([ProductID] = @Original_ProductID) AND ([Qty] = @Original_Qty) AND ([Price] = @Original_Price) AND ([TotalAmount] = @Original_TotalAmount));
SELECT SP_ID, StockID, ProductID, Qty, Price, TotalAmount FROM Stock_Product WHERE (SP_ID = @SP_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StockID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StockID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Price" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Price" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SP_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SP_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StockID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StockID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Price" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Price" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_TotalAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="TotalAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SP_ID" ColumnName="SP_ID" DataSourceName="INV_DB.dbo.Stock_Product" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@SP_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="SP_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="SP_ID" DataSetColumn="SP_ID" />
              <Mapping SourceColumn="StockID" DataSetColumn="StockID" />
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="Qty" DataSetColumn="Qty" />
              <Mapping SourceColumn="Price" DataSetColumn="Price" />
              <Mapping SourceColumn="TotalAmount" DataSetColumn="TotalAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SubCategoryTableAdapter" GeneratorDataComponentClassName="SubCategoryTableAdapter" Name="SubCategory" UserDataComponentName="SubCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.SubCategory" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[SubCategory] WHERE (([ID] = @Original_ID) AND ([SubCategoryName] = @Original_SubCategoryName) AND ([Category] = @Original_Category))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SubCategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SubCategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Category" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Category" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[SubCategory] ([ID], [SubCategoryName], [Category]) VALUES (@ID, @SubCategoryName, @Category);
SELECT ID, SubCategoryName, Category FROM SubCategory WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SubCategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SubCategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Category" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Category" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, SubCategoryName, Category FROM dbo.SubCategory</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[SubCategory] SET [ID] = @ID, [SubCategoryName] = @SubCategoryName, [Category] = @Category WHERE (([ID] = @Original_ID) AND ([SubCategoryName] = @Original_SubCategoryName) AND ([Category] = @Original_Category));
SELECT ID, SubCategoryName, Category FROM SubCategory WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SubCategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SubCategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Category" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Category" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SubCategoryName" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SubCategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Category" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Category" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="SubCategoryName" DataSetColumn="SubCategoryName" />
              <Mapping SourceColumn="Category" DataSetColumn="Category" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SupplierTableAdapter" GeneratorDataComponentClassName="SupplierTableAdapter" Name="Supplier" UserDataComponentName="SupplierTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Supplier" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Supplier] WHERE (([ID] = @Original_ID) AND ([SupplierID] = @Original_SupplierID) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ((@IsNull_Address = 1 AND [Address] IS NULL) OR ([Address] = @Original_Address)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_State = 1 AND [State] IS NULL) OR ([State] = @Original_State)) AND ((@IsNull_ZipCode = 1 AND [ZipCode] IS NULL) OR ([ZipCode] = @Original_ZipCode)) AND ((@IsNull_ContactNo = 1 AND [ContactNo] IS NULL) OR ([ContactNo] = @Original_ContactNo)) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SupplierID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Address" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_State" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ZipCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Supplier] ([ID], [SupplierID], [Name], [Address], [City], [State], [ZipCode], [ContactNo], [EmailID], [Remarks]) VALUES (@ID, @SupplierID, @Name, @Address, @City, @State, @ZipCode, @ContactNo, @EmailID, @Remarks);
SELECT ID, SupplierID, Name, Address, City, State, ZipCode, ContactNo, EmailID, Remarks FROM Supplier WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SupplierID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ID, SupplierID, Name, Address, City, State, ZipCode, ContactNo, EmailID, Remarks FROM dbo.Supplier</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Supplier] SET [ID] = @ID, [SupplierID] = @SupplierID, [Name] = @Name, [Address] = @Address, [City] = @City, [State] = @State, [ZipCode] = @ZipCode, [ContactNo] = @ContactNo, [EmailID] = @EmailID, [Remarks] = @Remarks WHERE (([ID] = @Original_ID) AND ([SupplierID] = @Original_SupplierID) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ((@IsNull_Address = 1 AND [Address] IS NULL) OR ([Address] = @Original_Address)) AND ((@IsNull_City = 1 AND [City] IS NULL) OR ([City] = @Original_City)) AND ((@IsNull_State = 1 AND [State] IS NULL) OR ([State] = @Original_State)) AND ((@IsNull_ZipCode = 1 AND [ZipCode] IS NULL) OR ([ZipCode] = @Original_ZipCode)) AND ((@IsNull_ContactNo = 1 AND [ContactNo] IS NULL) OR ([ContactNo] = @Original_ContactNo)) AND ((@IsNull_EmailID = 1 AND [EmailID] IS NULL) OR ([EmailID] = @Original_EmailID)));
SELECT ID, SupplierID, Name, Address, City, State, ZipCode, ContactNo, EmailID, Remarks FROM Supplier WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@SupplierID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Remarks" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Remarks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_SupplierID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="SupplierID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Address" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Address" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Address" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_City" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_City" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="City" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_State" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_State" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="State" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ZipCode" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ZipCode" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ZipCode" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_ContactNo" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_ContactNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="ContactNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_EmailID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_EmailID" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="EmailID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="SupplierID" DataSetColumn="SupplierID" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Address" DataSetColumn="Address" />
              <Mapping SourceColumn="City" DataSetColumn="City" />
              <Mapping SourceColumn="State" DataSetColumn="State" />
              <Mapping SourceColumn="ZipCode" DataSetColumn="ZipCode" />
              <Mapping SourceColumn="ContactNo" DataSetColumn="ContactNo" />
              <Mapping SourceColumn="EmailID" DataSetColumn="EmailID" />
              <Mapping SourceColumn="Remarks" DataSetColumn="Remarks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Temp_StockTableAdapter" GeneratorDataComponentClassName="Temp_StockTableAdapter" Name="Temp_Stock" UserDataComponentName="Temp_StockTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Temp_Stock" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Temp_Stock] WHERE (([ProductID] = @Original_ProductID) AND ([Qty] = @Original_Qty))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Temp_Stock] ([ProductID], [Qty]) VALUES (@ProductID, @Qty);
SELECT ProductID, Qty FROM Temp_Stock WHERE (ProductID = @ProductID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ProductID, Qty FROM dbo.Temp_Stock</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Temp_Stock] SET [ProductID] = @ProductID, [Qty] = @Qty WHERE (([ProductID] = @Original_ProductID) AND ([Qty] = @Original_Qty));
SELECT ProductID, Qty FROM Temp_Stock WHERE (ProductID = @ProductID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ProductID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ProductID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Qty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Qty" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ProductID" DataSetColumn="ProductID" />
              <Mapping SourceColumn="Qty" DataSetColumn="Qty" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="VoucherTableAdapter" GeneratorDataComponentClassName="VoucherTableAdapter" Name="Voucher" UserDataComponentName="VoucherTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Voucher" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Voucher] WHERE (([Id] = @Original_Id) AND ([VoucherNo] = @Original_VoucherNo) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ([Date] = @Original_Date) AND ((@IsNull_Details = 1 AND [Details] IS NULL) OR ([Details] = @Original_Details)) AND ([GrandTotal] = @Original_GrandTotal))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_VoucherNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="VoucherNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Details" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Details" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Voucher] ([Id], [VoucherNo], [Name], [Date], [Details], [GrandTotal]) VALUES (@Id, @VoucherNo, @Name, @Date, @Details, @GrandTotal);
SELECT Id, VoucherNo, Name, Date, Details, GrandTotal FROM Voucher WHERE (Id = @Id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@VoucherNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="VoucherNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Details" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Id, VoucherNo, Name, Date, Details, GrandTotal FROM dbo.Voucher</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Voucher] SET [Id] = @Id, [VoucherNo] = @VoucherNo, [Name] = @Name, [Date] = @Date, [Details] = @Details, [GrandTotal] = @GrandTotal WHERE (([Id] = @Original_Id) AND ([VoucherNo] = @Original_VoucherNo) AND ((@IsNull_Name = 1 AND [Name] IS NULL) OR ([Name] = @Original_Name)) AND ([Date] = @Original_Date) AND ((@IsNull_Details = 1 AND [Details] IS NULL) OR ([Details] = @Original_Details)) AND ([GrandTotal] = @Original_GrandTotal));
SELECT Id, VoucherNo, Name, Date, Details, GrandTotal FROM Voucher WHERE (Id = @Id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@VoucherNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="VoucherNo" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Details" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_VoucherNo" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="VoucherNo" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Name" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Name" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Name" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_Date" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Date" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@IsNull_Details" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="true" SourceVersion="Original" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Details" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Details" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_GrandTotal" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="GrandTotal" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="Id" DataSetColumn="Id" />
              <Mapping SourceColumn="VoucherNo" DataSetColumn="VoucherNo" />
              <Mapping SourceColumn="Name" DataSetColumn="Name" />
              <Mapping SourceColumn="Date" DataSetColumn="Date" />
              <Mapping SourceColumn="Details" DataSetColumn="Details" />
              <Mapping SourceColumn="GrandTotal" DataSetColumn="GrandTotal" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="Voucher_OtherDetailsTableAdapter" GeneratorDataComponentClassName="Voucher_OtherDetailsTableAdapter" Name="Voucher_OtherDetails" UserDataComponentName="Voucher_OtherDetailsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="SIS_DBConnectionString1 (MySettings)" DbObjectName="INV_DB.dbo.Voucher_OtherDetails" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [dbo].[Voucher_OtherDetails] WHERE (([VD_ID] = @Original_VD_ID) AND ([VoucherID] = @Original_VoucherID) AND ([Particulars] = @Original_Particulars) AND ([Amount] = @Original_Amount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_VD_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VD_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_VoucherID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VoucherID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Particulars" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Particulars" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [dbo].[Voucher_OtherDetails] ([VoucherID], [Particulars], [Amount], [Note]) VALUES (@VoucherID, @Particulars, @Amount, @Note);
SELECT VD_ID, VoucherID, Particulars, Amount, Note FROM Voucher_OtherDetails WHERE (VD_ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@VoucherID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VoucherID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Particulars" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Particulars" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Note" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Note" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT VD_ID, VoucherID, Particulars, Amount, Note FROM dbo.Voucher_OtherDetails</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [dbo].[Voucher_OtherDetails] SET [VoucherID] = @VoucherID, [Particulars] = @Particulars, [Amount] = @Amount, [Note] = @Note WHERE (([VD_ID] = @Original_VD_ID) AND ([VoucherID] = @Original_VoucherID) AND ([Particulars] = @Original_Particulars) AND ([Amount] = @Original_Amount));
SELECT VD_ID, VoucherID, Particulars, Amount, Note FROM Voucher_OtherDetails WHERE (VD_ID = @VD_ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@VoucherID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VoucherID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Particulars" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Particulars" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Note" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Note" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_VD_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VD_ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_VoucherID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="VoucherID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="StringFixedLength" Direction="Input" ParameterName="@Original_Particulars" Precision="0" ProviderType="NChar" Scale="0" Size="0" SourceColumn="Particulars" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_Amount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="VD_ID" ColumnName="VD_ID" DataSourceName="INV_DB.dbo.Voucher_OtherDetails" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@VD_ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="VD_ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="VD_ID" DataSetColumn="VD_ID" />
              <Mapping SourceColumn="VoucherID" DataSetColumn="VoucherID" />
              <Mapping SourceColumn="Particulars" DataSetColumn="Particulars" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="Note" DataSetColumn="Note" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="SIS_DBDataSet" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="True" msprop:Generator_DataSetName="SIS_DBDataSet" msprop:Generator_UserDSName="SIS_DBDataSet">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Activation" msprop:Generator_TableClassName="ActivationDataTable" msprop:Generator_TableVarName="tableActivation" msprop:Generator_RowChangedName="ActivationRowChanged" msprop:Generator_TablePropName="Activation" msprop:Generator_RowDeletingName="ActivationRowDeleting" msprop:Generator_RowChangingName="ActivationRowChanging" msprop:Generator_RowEvHandlerName="ActivationRowChangeEventHandler" msprop:Generator_RowDeletedName="ActivationRowDeleted" msprop:Generator_RowClassName="ActivationRow" msprop:Generator_UserTableName="Activation" msprop:Generator_RowEvArgName="ActivationRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="HardwareID" msprop:Generator_ColumnVarNameInTable="columnHardwareID" msprop:Generator_ColumnPropNameInRow="HardwareID" msprop:Generator_ColumnPropNameInTable="HardwareIDColumn" msprop:Generator_UserColumnName="HardwareID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SerialNo" msprop:Generator_ColumnVarNameInTable="columnSerialNo" msprop:Generator_ColumnPropNameInRow="SerialNo" msprop:Generator_ColumnPropNameInTable="SerialNoColumn" msprop:Generator_UserColumnName="SerialNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ActivationID" msprop:Generator_ColumnVarNameInTable="columnActivationID" msprop:Generator_ColumnPropNameInRow="ActivationID" msprop:Generator_ColumnPropNameInTable="ActivationIDColumn" msprop:Generator_UserColumnName="ActivationID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Category" msprop:Generator_TableClassName="CategoryDataTable" msprop:Generator_TableVarName="tableCategory" msprop:Generator_RowChangedName="CategoryRowChanged" msprop:Generator_TablePropName="Category" msprop:Generator_RowDeletingName="CategoryRowDeleting" msprop:Generator_RowChangingName="CategoryRowChanging" msprop:Generator_RowEvHandlerName="CategoryRowChangeEventHandler" msprop:Generator_RowDeletedName="CategoryRowDeleted" msprop:Generator_RowClassName="CategoryRow" msprop:Generator_UserTableName="Category" msprop:Generator_RowEvArgName="CategoryRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Company" msprop:Generator_TableClassName="CompanyDataTable" msprop:Generator_TableVarName="tableCompany" msprop:Generator_RowChangedName="CompanyRowChanged" msprop:Generator_TablePropName="Company" msprop:Generator_RowDeletingName="CompanyRowDeleting" msprop:Generator_RowChangingName="CompanyRowChanging" msprop:Generator_RowEvHandlerName="CompanyRowChangeEventHandler" msprop:Generator_RowDeletedName="CompanyRowDeleted" msprop:Generator_RowClassName="CompanyRow" msprop:Generator_UserTableName="Company" msprop:Generator_RowEvArgName="CompanyRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="CompanyName" msprop:Generator_ColumnVarNameInTable="columnCompanyName" msprop:Generator_ColumnPropNameInRow="CompanyName" msprop:Generator_ColumnPropNameInTable="CompanyNameColumn" msprop:Generator_UserColumnName="CompanyName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_UserColumnName="Address">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Logo" msprop:Generator_ColumnVarNameInTable="columnLogo" msprop:Generator_ColumnPropNameInRow="Logo" msprop:Generator_ColumnPropNameInTable="LogoColumn" msprop:Generator_UserColumnName="Logo" type="xs:base64Binary" />
              <xs:element name="TIN" msprop:Generator_ColumnVarNameInTable="columnTIN" msprop:Generator_ColumnPropNameInRow="TIN" msprop:Generator_ColumnPropNameInTable="TINColumn" msprop:Generator_UserColumnName="TIN" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="STNo" msprop:Generator_ColumnVarNameInTable="columnSTNo" msprop:Generator_ColumnPropNameInRow="STNo" msprop:Generator_ColumnPropNameInTable="STNoColumn" msprop:Generator_UserColumnName="STNo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CIN" msprop:Generator_ColumnVarNameInTable="columnCIN" msprop:Generator_ColumnPropNameInRow="CIN" msprop:Generator_ColumnPropNameInTable="CINColumn" msprop:Generator_UserColumnName="CIN" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Company_Contacts" msprop:Generator_TableClassName="Company_ContactsDataTable" msprop:Generator_TableVarName="tableCompany_Contacts" msprop:Generator_RowChangedName="Company_ContactsRowChanged" msprop:Generator_TablePropName="Company_Contacts" msprop:Generator_RowDeletingName="Company_ContactsRowDeleting" msprop:Generator_RowChangingName="Company_ContactsRowChanging" msprop:Generator_RowEvHandlerName="Company_ContactsRowChangeEventHandler" msprop:Generator_RowDeletedName="Company_ContactsRowDeleted" msprop:Generator_RowClassName="Company_ContactsRow" msprop:Generator_UserTableName="Company_Contacts" msprop:Generator_RowEvArgName="Company_ContactsRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_UserColumnName="Id" type="xs:int" />
              <xs:element name="ContactPerson" msprop:Generator_ColumnVarNameInTable="columnContactPerson" msprop:Generator_ColumnPropNameInRow="ContactPerson" msprop:Generator_ColumnPropNameInTable="ContactPersonColumn" msprop:Generator_UserColumnName="ContactPerson">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Customer" msprop:Generator_TableClassName="CustomerDataTable" msprop:Generator_TableVarName="tableCustomer" msprop:Generator_RowChangedName="CustomerRowChanged" msprop:Generator_TablePropName="Customer" msprop:Generator_RowDeletingName="CustomerRowDeleting" msprop:Generator_RowChangingName="CustomerRowChanging" msprop:Generator_RowEvHandlerName="CustomerRowChangeEventHandler" msprop:Generator_RowDeletedName="CustomerRowDeleted" msprop:Generator_RowClassName="CustomerRow" msprop:Generator_UserTableName="Customer" msprop:Generator_RowEvArgName="CustomerRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="CustomerID" msprop:Generator_ColumnVarNameInTable="columnCustomerID" msprop:Generator_ColumnPropNameInRow="CustomerID" msprop:Generator_ColumnPropNameInTable="CustomerIDColumn" msprop:Generator_UserColumnName="CustomerID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Gender" msprop:Generator_ColumnVarNameInTable="columnGender" msprop:Generator_ColumnPropNameInRow="Gender" msprop:Generator_ColumnPropNameInTable="GenderColumn" msprop:Generator_UserColumnName="Gender" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_UserColumnName="Address" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_UserColumnName="City" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="State" msprop:Generator_ColumnVarNameInTable="columnState" msprop:Generator_ColumnPropNameInRow="State" msprop:Generator_ColumnPropNameInTable="StateColumn" msprop:Generator_UserColumnName="State" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ZipCode" msprop:Generator_ColumnVarNameInTable="columnZipCode" msprop:Generator_ColumnPropNameInRow="ZipCode" msprop:Generator_ColumnPropNameInTable="ZipCodeColumn" msprop:Generator_UserColumnName="ZipCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Photo" msprop:Generator_ColumnVarNameInTable="columnPhoto" msprop:Generator_ColumnPropNameInRow="Photo" msprop:Generator_ColumnPropNameInTable="PhotoColumn" msprop:Generator_UserColumnName="Photo" type="xs:base64Binary" minOccurs="0" />
              <xs:element name="CustomerType" msprop:Generator_ColumnVarNameInTable="columnCustomerType" msprop:Generator_ColumnPropNameInRow="CustomerType" msprop:Generator_ColumnPropNameInTable="CustomerTypeColumn" msprop:Generator_UserColumnName="CustomerType" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Invoice_Payment" msprop:Generator_TableClassName="Invoice_PaymentDataTable" msprop:Generator_TableVarName="tableInvoice_Payment" msprop:Generator_RowChangedName="Invoice_PaymentRowChanged" msprop:Generator_TablePropName="Invoice_Payment" msprop:Generator_RowDeletingName="Invoice_PaymentRowDeleting" msprop:Generator_RowChangingName="Invoice_PaymentRowChanging" msprop:Generator_RowEvHandlerName="Invoice_PaymentRowChangeEventHandler" msprop:Generator_RowDeletedName="Invoice_PaymentRowDeleted" msprop:Generator_RowClassName="Invoice_PaymentRow" msprop:Generator_UserTableName="Invoice_Payment" msprop:Generator_RowEvArgName="Invoice_PaymentRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IP_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnIP_ID" msprop:Generator_ColumnPropNameInRow="IP_ID" msprop:Generator_ColumnPropNameInTable="IP_IDColumn" msprop:Generator_UserColumnName="IP_ID" type="xs:int" />
              <xs:element name="InvoiceID" msprop:Generator_ColumnVarNameInTable="columnInvoiceID" msprop:Generator_ColumnPropNameInRow="InvoiceID" msprop:Generator_ColumnPropNameInTable="InvoiceIDColumn" msprop:Generator_UserColumnName="InvoiceID" type="xs:int" />
              <xs:element name="PaymentDate" msprop:Generator_ColumnVarNameInTable="columnPaymentDate" msprop:Generator_ColumnPropNameInRow="PaymentDate" msprop:Generator_ColumnPropNameInTable="PaymentDateColumn" msprop:Generator_UserColumnName="PaymentDate" type="xs:dateTime" />
              <xs:element name="TotalPaid" msprop:Generator_ColumnVarNameInTable="columnTotalPaid" msprop:Generator_ColumnPropNameInRow="TotalPaid" msprop:Generator_ColumnPropNameInTable="TotalPaidColumn" msprop:Generator_UserColumnName="TotalPaid" type="xs:decimal" />
              <xs:element name="PaymentMode" msprop:Generator_ColumnVarNameInTable="columnPaymentMode" msprop:Generator_ColumnPropNameInRow="PaymentMode" msprop:Generator_ColumnPropNameInTable="PaymentModeColumn" msprop:Generator_UserColumnName="PaymentMode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Invoice_Product" msprop:Generator_TableClassName="Invoice_ProductDataTable" msprop:Generator_TableVarName="tableInvoice_Product" msprop:Generator_RowChangedName="Invoice_ProductRowChanged" msprop:Generator_TablePropName="Invoice_Product" msprop:Generator_RowDeletingName="Invoice_ProductRowDeleting" msprop:Generator_RowChangingName="Invoice_ProductRowChanging" msprop:Generator_RowEvHandlerName="Invoice_ProductRowChangeEventHandler" msprop:Generator_RowDeletedName="Invoice_ProductRowDeleted" msprop:Generator_RowClassName="Invoice_ProductRow" msprop:Generator_UserTableName="Invoice_Product" msprop:Generator_RowEvArgName="Invoice_ProductRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IPo_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnIPo_ID" msprop:Generator_ColumnPropNameInRow="IPo_ID" msprop:Generator_ColumnPropNameInTable="IPo_IDColumn" msprop:Generator_UserColumnName="IPo_ID" type="xs:int" />
              <xs:element name="InvoiceID" msprop:Generator_ColumnVarNameInTable="columnInvoiceID" msprop:Generator_ColumnPropNameInRow="InvoiceID" msprop:Generator_ColumnPropNameInTable="InvoiceIDColumn" msprop:Generator_UserColumnName="InvoiceID" type="xs:int" />
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="CostPrice" msprop:Generator_ColumnVarNameInTable="columnCostPrice" msprop:Generator_ColumnPropNameInRow="CostPrice" msprop:Generator_ColumnPropNameInTable="CostPriceColumn" msprop:Generator_UserColumnName="CostPrice" type="xs:decimal" />
              <xs:element name="SellingPrice" msprop:Generator_ColumnVarNameInTable="columnSellingPrice" msprop:Generator_ColumnPropNameInRow="SellingPrice" msprop:Generator_ColumnPropNameInTable="SellingPriceColumn" msprop:Generator_UserColumnName="SellingPrice" type="xs:decimal" />
              <xs:element name="Margin" msprop:Generator_ColumnVarNameInTable="columnMargin" msprop:Generator_ColumnPropNameInRow="Margin" msprop:Generator_ColumnPropNameInTable="MarginColumn" msprop:Generator_UserColumnName="Margin" type="xs:decimal" />
              <xs:element name="Qty" msprop:Generator_ColumnVarNameInTable="columnQty" msprop:Generator_ColumnPropNameInRow="Qty" msprop:Generator_ColumnPropNameInTable="QtyColumn" msprop:Generator_UserColumnName="Qty" type="xs:int" />
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="DiscountPer" msprop:Generator_ColumnVarNameInTable="columnDiscountPer" msprop:Generator_ColumnPropNameInRow="DiscountPer" msprop:Generator_ColumnPropNameInTable="DiscountPerColumn" msprop:Generator_UserColumnName="DiscountPer" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="VATPer" msprop:Generator_ColumnVarNameInTable="columnVATPer" msprop:Generator_ColumnPropNameInRow="VATPer" msprop:Generator_ColumnPropNameInTable="VATPerColumn" msprop:Generator_UserColumnName="VATPer" type="xs:decimal" />
              <xs:element name="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnPropNameInTable="VATColumn" msprop:Generator_UserColumnName="VAT" type="xs:decimal" />
              <xs:element name="TotalAmount" msprop:Generator_ColumnVarNameInTable="columnTotalAmount" msprop:Generator_ColumnPropNameInRow="TotalAmount" msprop:Generator_ColumnPropNameInTable="TotalAmountColumn" msprop:Generator_UserColumnName="TotalAmount" type="xs:decimal" />
              <xs:element name="Barcode" msprop:Generator_ColumnVarNameInTable="columnBarcode" msprop:Generator_ColumnPropNameInRow="Barcode" msprop:Generator_ColumnPropNameInTable="BarcodeColumn" msprop:Generator_UserColumnName="Barcode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Invoice1_Payment" msprop:Generator_TableClassName="Invoice1_PaymentDataTable" msprop:Generator_TableVarName="tableInvoice1_Payment" msprop:Generator_RowChangedName="Invoice1_PaymentRowChanged" msprop:Generator_TablePropName="Invoice1_Payment" msprop:Generator_RowDeletingName="Invoice1_PaymentRowDeleting" msprop:Generator_RowChangingName="Invoice1_PaymentRowChanging" msprop:Generator_RowEvHandlerName="Invoice1_PaymentRowChangeEventHandler" msprop:Generator_RowDeletedName="Invoice1_PaymentRowDeleted" msprop:Generator_RowClassName="Invoice1_PaymentRow" msprop:Generator_UserTableName="Invoice1_Payment" msprop:Generator_RowEvArgName="Invoice1_PaymentRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IP_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnIP_ID" msprop:Generator_ColumnPropNameInRow="IP_ID" msprop:Generator_ColumnPropNameInTable="IP_IDColumn" msprop:Generator_UserColumnName="IP_ID" type="xs:int" />
              <xs:element name="InvoiceID" msprop:Generator_ColumnVarNameInTable="columnInvoiceID" msprop:Generator_ColumnPropNameInRow="InvoiceID" msprop:Generator_ColumnPropNameInTable="InvoiceIDColumn" msprop:Generator_UserColumnName="InvoiceID" type="xs:int" />
              <xs:element name="PaymentDate" msprop:Generator_ColumnVarNameInTable="columnPaymentDate" msprop:Generator_ColumnPropNameInRow="PaymentDate" msprop:Generator_ColumnPropNameInTable="PaymentDateColumn" msprop:Generator_UserColumnName="PaymentDate" type="xs:dateTime" />
              <xs:element name="TotalPaid" msprop:Generator_ColumnVarNameInTable="columnTotalPaid" msprop:Generator_ColumnPropNameInRow="TotalPaid" msprop:Generator_ColumnPropNameInTable="TotalPaidColumn" msprop:Generator_UserColumnName="TotalPaid" type="xs:decimal" />
              <xs:element name="PaymentMode" msprop:Generator_ColumnVarNameInTable="columnPaymentMode" msprop:Generator_ColumnPropNameInRow="PaymentMode" msprop:Generator_ColumnPropNameInTable="PaymentModeColumn" msprop:Generator_UserColumnName="PaymentMode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Invoice1_Product" msprop:Generator_TableClassName="Invoice1_ProductDataTable" msprop:Generator_TableVarName="tableInvoice1_Product" msprop:Generator_RowChangedName="Invoice1_ProductRowChanged" msprop:Generator_TablePropName="Invoice1_Product" msprop:Generator_RowDeletingName="Invoice1_ProductRowDeleting" msprop:Generator_RowChangingName="Invoice1_ProductRowChanging" msprop:Generator_RowEvHandlerName="Invoice1_ProductRowChangeEventHandler" msprop:Generator_RowDeletedName="Invoice1_ProductRowDeleted" msprop:Generator_RowClassName="Invoice1_ProductRow" msprop:Generator_UserTableName="Invoice1_Product" msprop:Generator_RowEvArgName="Invoice1_ProductRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Ipo_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnIpo_ID" msprop:Generator_ColumnPropNameInRow="Ipo_ID" msprop:Generator_ColumnPropNameInTable="Ipo_IDColumn" msprop:Generator_UserColumnName="Ipo_ID" type="xs:int" />
              <xs:element name="InvoiceID" msprop:Generator_ColumnVarNameInTable="columnInvoiceID" msprop:Generator_ColumnPropNameInRow="InvoiceID" msprop:Generator_ColumnPropNameInTable="InvoiceIDColumn" msprop:Generator_UserColumnName="InvoiceID" type="xs:int" />
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="CostPrice" msprop:Generator_ColumnVarNameInTable="columnCostPrice" msprop:Generator_ColumnPropNameInRow="CostPrice" msprop:Generator_ColumnPropNameInTable="CostPriceColumn" msprop:Generator_UserColumnName="CostPrice" type="xs:decimal" />
              <xs:element name="SellingPrice" msprop:Generator_ColumnVarNameInTable="columnSellingPrice" msprop:Generator_ColumnPropNameInRow="SellingPrice" msprop:Generator_ColumnPropNameInTable="SellingPriceColumn" msprop:Generator_UserColumnName="SellingPrice" type="xs:decimal" />
              <xs:element name="Margin" msprop:Generator_ColumnVarNameInTable="columnMargin" msprop:Generator_ColumnPropNameInRow="Margin" msprop:Generator_ColumnPropNameInTable="MarginColumn" msprop:Generator_UserColumnName="Margin" type="xs:decimal" />
              <xs:element name="Qty" msprop:Generator_ColumnVarNameInTable="columnQty" msprop:Generator_ColumnPropNameInRow="Qty" msprop:Generator_ColumnPropNameInTable="QtyColumn" msprop:Generator_UserColumnName="Qty" type="xs:int" />
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="DiscountPer" msprop:Generator_ColumnVarNameInTable="columnDiscountPer" msprop:Generator_ColumnPropNameInRow="DiscountPer" msprop:Generator_ColumnPropNameInTable="DiscountPerColumn" msprop:Generator_UserColumnName="DiscountPer" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="VATPer" msprop:Generator_ColumnVarNameInTable="columnVATPer" msprop:Generator_ColumnPropNameInRow="VATPer" msprop:Generator_ColumnPropNameInTable="VATPerColumn" msprop:Generator_UserColumnName="VATPer" type="xs:decimal" />
              <xs:element name="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnPropNameInTable="VATColumn" msprop:Generator_UserColumnName="VAT" type="xs:decimal" />
              <xs:element name="TotalAmount" msprop:Generator_ColumnVarNameInTable="columnTotalAmount" msprop:Generator_ColumnPropNameInRow="TotalAmount" msprop:Generator_ColumnPropNameInTable="TotalAmountColumn" msprop:Generator_UserColumnName="TotalAmount" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InvoiceInfo" msprop:Generator_TableClassName="InvoiceInfoDataTable" msprop:Generator_TableVarName="tableInvoiceInfo" msprop:Generator_RowChangedName="InvoiceInfoRowChanged" msprop:Generator_TablePropName="InvoiceInfo" msprop:Generator_RowDeletingName="InvoiceInfoRowDeleting" msprop:Generator_RowChangingName="InvoiceInfoRowChanging" msprop:Generator_RowEvHandlerName="InvoiceInfoRowChangeEventHandler" msprop:Generator_RowDeletedName="InvoiceInfoRowDeleted" msprop:Generator_RowClassName="InvoiceInfoRow" msprop:Generator_UserTableName="InvoiceInfo" msprop:Generator_RowEvArgName="InvoiceInfoRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Inv_ID" msprop:Generator_ColumnVarNameInTable="columnInv_ID" msprop:Generator_ColumnPropNameInRow="Inv_ID" msprop:Generator_ColumnPropNameInTable="Inv_IDColumn" msprop:Generator_UserColumnName="Inv_ID" type="xs:int" />
              <xs:element name="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" msprop:Generator_UserColumnName="InvoiceNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InvoiceDate" msprop:Generator_ColumnVarNameInTable="columnInvoiceDate" msprop:Generator_ColumnPropNameInRow="InvoiceDate" msprop:Generator_ColumnPropNameInTable="InvoiceDateColumn" msprop:Generator_UserColumnName="InvoiceDate" type="xs:dateTime" />
              <xs:element name="CustomerID" msprop:Generator_ColumnVarNameInTable="columnCustomerID" msprop:Generator_ColumnPropNameInRow="CustomerID" msprop:Generator_ColumnPropNameInTable="CustomerIDColumn" msprop:Generator_UserColumnName="CustomerID" type="xs:int" />
              <xs:element name="GrandTotal" msprop:Generator_ColumnVarNameInTable="columnGrandTotal" msprop:Generator_ColumnPropNameInRow="GrandTotal" msprop:Generator_ColumnPropNameInTable="GrandTotalColumn" msprop:Generator_UserColumnName="GrandTotal" type="xs:decimal" />
              <xs:element name="TotalPaid" msprop:Generator_ColumnVarNameInTable="columnTotalPaid" msprop:Generator_ColumnPropNameInRow="TotalPaid" msprop:Generator_ColumnPropNameInTable="TotalPaidColumn" msprop:Generator_UserColumnName="TotalPaid" type="xs:decimal" />
              <xs:element name="Balance" msprop:Generator_ColumnVarNameInTable="columnBalance" msprop:Generator_ColumnPropNameInRow="Balance" msprop:Generator_ColumnPropNameInTable="BalanceColumn" msprop:Generator_UserColumnName="Balance" type="xs:decimal" />
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InvoiceInfo1" msprop:Generator_TableClassName="InvoiceInfo1DataTable" msprop:Generator_TableVarName="tableInvoiceInfo1" msprop:Generator_RowChangedName="InvoiceInfo1RowChanged" msprop:Generator_TablePropName="InvoiceInfo1" msprop:Generator_RowDeletingName="InvoiceInfo1RowDeleting" msprop:Generator_RowChangingName="InvoiceInfo1RowChanging" msprop:Generator_RowEvHandlerName="InvoiceInfo1RowChangeEventHandler" msprop:Generator_RowDeletedName="InvoiceInfo1RowDeleted" msprop:Generator_RowClassName="InvoiceInfo1Row" msprop:Generator_UserTableName="InvoiceInfo1" msprop:Generator_RowEvArgName="InvoiceInfo1RowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Inv_ID" msprop:Generator_ColumnVarNameInTable="columnInv_ID" msprop:Generator_ColumnPropNameInRow="Inv_ID" msprop:Generator_ColumnPropNameInTable="Inv_IDColumn" msprop:Generator_UserColumnName="Inv_ID" type="xs:int" />
              <xs:element name="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" msprop:Generator_UserColumnName="InvoiceNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InvoiceDate" msprop:Generator_ColumnVarNameInTable="columnInvoiceDate" msprop:Generator_ColumnPropNameInRow="InvoiceDate" msprop:Generator_ColumnPropNameInTable="InvoiceDateColumn" msprop:Generator_UserColumnName="InvoiceDate" type="xs:dateTime" />
              <xs:element name="ServiceID" msprop:Generator_ColumnVarNameInTable="columnServiceID" msprop:Generator_ColumnPropNameInRow="ServiceID" msprop:Generator_ColumnPropNameInTable="ServiceIDColumn" msprop:Generator_UserColumnName="ServiceID" type="xs:int" />
              <xs:element name="RepairCharges" msprop:Generator_ColumnVarNameInTable="columnRepairCharges" msprop:Generator_ColumnPropNameInRow="RepairCharges" msprop:Generator_ColumnPropNameInTable="RepairChargesColumn" msprop:Generator_UserColumnName="RepairCharges" type="xs:decimal" />
              <xs:element name="Upfront" msprop:Generator_ColumnVarNameInTable="columnUpfront" msprop:Generator_ColumnPropNameInRow="Upfront" msprop:Generator_ColumnPropNameInTable="UpfrontColumn" msprop:Generator_UserColumnName="Upfront" type="xs:decimal" />
              <xs:element name="ProductCharges" msprop:Generator_ColumnVarNameInTable="columnProductCharges" msprop:Generator_ColumnPropNameInRow="ProductCharges" msprop:Generator_ColumnPropNameInTable="ProductChargesColumn" msprop:Generator_UserColumnName="ProductCharges" type="xs:decimal" />
              <xs:element name="ServiceTaxPer" msprop:Generator_ColumnVarNameInTable="columnServiceTaxPer" msprop:Generator_ColumnPropNameInRow="ServiceTaxPer" msprop:Generator_ColumnPropNameInTable="ServiceTaxPerColumn" msprop:Generator_UserColumnName="ServiceTaxPer" type="xs:decimal" />
              <xs:element name="ServiceTax" msprop:Generator_ColumnVarNameInTable="columnServiceTax" msprop:Generator_ColumnPropNameInRow="ServiceTax" msprop:Generator_ColumnPropNameInTable="ServiceTaxColumn" msprop:Generator_UserColumnName="ServiceTax" type="xs:decimal" />
              <xs:element name="GrandTotal" msprop:Generator_ColumnVarNameInTable="columnGrandTotal" msprop:Generator_ColumnPropNameInRow="GrandTotal" msprop:Generator_ColumnPropNameInTable="GrandTotalColumn" msprop:Generator_UserColumnName="GrandTotal" type="xs:decimal" />
              <xs:element name="TotalPaid" msprop:Generator_ColumnVarNameInTable="columnTotalPaid" msprop:Generator_ColumnPropNameInRow="TotalPaid" msprop:Generator_ColumnPropNameInTable="TotalPaidColumn" msprop:Generator_UserColumnName="TotalPaid" type="xs:decimal" />
              <xs:element name="Balance" msprop:Generator_ColumnVarNameInTable="columnBalance" msprop:Generator_ColumnPropNameInRow="Balance" msprop:Generator_ColumnPropNameInTable="BalanceColumn" msprop:Generator_UserColumnName="Balance" type="xs:decimal" />
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Logs" msprop:Generator_TableClassName="LogsDataTable" msprop:Generator_TableVarName="tableLogs" msprop:Generator_RowChangedName="LogsRowChanged" msprop:Generator_TablePropName="Logs" msprop:Generator_RowDeletingName="LogsRowDeleting" msprop:Generator_RowChangingName="LogsRowChanging" msprop:Generator_RowEvHandlerName="LogsRowChangeEventHandler" msprop:Generator_RowDeletedName="LogsRowDeleted" msprop:Generator_RowClassName="LogsRow" msprop:Generator_UserTableName="Logs" msprop:Generator_RowEvArgName="LogsRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="UserID" msprop:Generator_ColumnVarNameInTable="columnUserID" msprop:Generator_ColumnPropNameInRow="UserID" msprop:Generator_ColumnPropNameInTable="UserIDColumn" msprop:Generator_UserColumnName="UserID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Operation" msprop:Generator_ColumnVarNameInTable="columnOperation" msprop:Generator_ColumnPropNameInRow="Operation" msprop:Generator_ColumnPropNameInTable="OperationColumn" msprop:Generator_UserColumnName="Operation">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="_Date" msprop:Generator_ColumnPropNameInTable="DateColumn" msprop:Generator_UserColumnName="Date" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Product" msprop:Generator_TableClassName="ProductDataTable" msprop:Generator_TableVarName="tableProduct" msprop:Generator_RowChangedName="ProductRowChanged" msprop:Generator_TablePropName="Product" msprop:Generator_RowDeletingName="ProductRowDeleting" msprop:Generator_RowChangingName="ProductRowChanging" msprop:Generator_RowEvHandlerName="ProductRowChangeEventHandler" msprop:Generator_RowDeletedName="ProductRowDeleted" msprop:Generator_RowClassName="ProductRow" msprop:Generator_UserTableName="Product" msprop:Generator_RowEvArgName="ProductRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="PID" msprop:Generator_ColumnVarNameInTable="columnPID" msprop:Generator_ColumnPropNameInRow="PID" msprop:Generator_ColumnPropNameInTable="PIDColumn" msprop:Generator_UserColumnName="PID" type="xs:int" />
              <xs:element name="ProductCode" msprop:Generator_ColumnVarNameInTable="columnProductCode" msprop:Generator_ColumnPropNameInRow="ProductCode" msprop:Generator_ColumnPropNameInTable="ProductCodeColumn" msprop:Generator_UserColumnName="ProductCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProductName" msprop:Generator_ColumnVarNameInTable="columnProductName" msprop:Generator_ColumnPropNameInRow="ProductName" msprop:Generator_ColumnPropNameInTable="ProductNameColumn" msprop:Generator_UserColumnName="ProductName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SubCategoryID" msprop:Generator_ColumnVarNameInTable="columnSubCategoryID" msprop:Generator_ColumnPropNameInRow="SubCategoryID" msprop:Generator_ColumnPropNameInTable="SubCategoryIDColumn" msprop:Generator_UserColumnName="SubCategoryID" type="xs:int" />
              <xs:element name="Description" msprop:Generator_ColumnVarNameInTable="columnDescription" msprop:Generator_ColumnPropNameInRow="Description" msprop:Generator_ColumnPropNameInTable="DescriptionColumn" msprop:Generator_UserColumnName="Description" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CostPrice" msprop:Generator_ColumnVarNameInTable="columnCostPrice" msprop:Generator_ColumnPropNameInRow="CostPrice" msprop:Generator_ColumnPropNameInTable="CostPriceColumn" msprop:Generator_UserColumnName="CostPrice" type="xs:decimal" />
              <xs:element name="SellingPrice" msprop:Generator_ColumnVarNameInTable="columnSellingPrice" msprop:Generator_ColumnPropNameInRow="SellingPrice" msprop:Generator_ColumnPropNameInTable="SellingPriceColumn" msprop:Generator_UserColumnName="SellingPrice" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnPropNameInTable="VATColumn" msprop:Generator_UserColumnName="VAT" type="xs:decimal" />
              <xs:element name="ReorderPoint" msprop:Generator_ColumnVarNameInTable="columnReorderPoint" msprop:Generator_ColumnPropNameInRow="ReorderPoint" msprop:Generator_ColumnPropNameInTable="ReorderPointColumn" msprop:Generator_UserColumnName="ReorderPoint" type="xs:int" />
              <xs:element name="OpeningStock" msprop:Generator_ColumnVarNameInTable="columnOpeningStock" msprop:Generator_ColumnPropNameInRow="OpeningStock" msprop:Generator_ColumnPropNameInTable="OpeningStockColumn" msprop:Generator_UserColumnName="OpeningStock" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Product_Join" msprop:Generator_TableClassName="Product_JoinDataTable" msprop:Generator_TableVarName="tableProduct_Join" msprop:Generator_RowChangedName="Product_JoinRowChanged" msprop:Generator_TablePropName="Product_Join" msprop:Generator_RowDeletingName="Product_JoinRowDeleting" msprop:Generator_RowChangingName="Product_JoinRowChanging" msprop:Generator_RowEvHandlerName="Product_JoinRowChangeEventHandler" msprop:Generator_RowDeletedName="Product_JoinRowDeleted" msprop:Generator_RowClassName="Product_JoinRow" msprop:Generator_UserTableName="Product_Join" msprop:Generator_RowEvArgName="Product_JoinRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_UserColumnName="Id" type="xs:int" />
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="Photo" msprop:Generator_ColumnVarNameInTable="columnPhoto" msprop:Generator_ColumnPropNameInRow="Photo" msprop:Generator_ColumnPropNameInTable="PhotoColumn" msprop:Generator_UserColumnName="Photo" type="xs:base64Binary" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Quotation" msprop:Generator_TableClassName="QuotationDataTable" msprop:Generator_TableVarName="tableQuotation" msprop:Generator_RowChangedName="QuotationRowChanged" msprop:Generator_TablePropName="Quotation" msprop:Generator_RowDeletingName="QuotationRowDeleting" msprop:Generator_RowChangingName="QuotationRowChanging" msprop:Generator_RowEvHandlerName="QuotationRowChangeEventHandler" msprop:Generator_RowDeletedName="QuotationRowDeleted" msprop:Generator_RowClassName="QuotationRow" msprop:Generator_UserTableName="Quotation" msprop:Generator_RowEvArgName="QuotationRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Q_ID" msprop:Generator_ColumnVarNameInTable="columnQ_ID" msprop:Generator_ColumnPropNameInRow="Q_ID" msprop:Generator_ColumnPropNameInTable="Q_IDColumn" msprop:Generator_UserColumnName="Q_ID" type="xs:int" />
              <xs:element name="QuotationNo" msprop:Generator_ColumnVarNameInTable="columnQuotationNo" msprop:Generator_ColumnPropNameInRow="QuotationNo" msprop:Generator_ColumnPropNameInTable="QuotationNoColumn" msprop:Generator_UserColumnName="QuotationNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="_Date" msprop:Generator_ColumnPropNameInTable="DateColumn" msprop:Generator_UserColumnName="Date" type="xs:dateTime" />
              <xs:element name="CustomerID" msprop:Generator_ColumnVarNameInTable="columnCustomerID" msprop:Generator_ColumnPropNameInRow="CustomerID" msprop:Generator_ColumnPropNameInTable="CustomerIDColumn" msprop:Generator_UserColumnName="CustomerID" type="xs:int" />
              <xs:element name="GrandTotal" msprop:Generator_ColumnVarNameInTable="columnGrandTotal" msprop:Generator_ColumnPropNameInRow="GrandTotal" msprop:Generator_ColumnPropNameInTable="GrandTotalColumn" msprop:Generator_UserColumnName="GrandTotal" type="xs:decimal" />
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Quotation_Join" msprop:Generator_TableClassName="Quotation_JoinDataTable" msprop:Generator_TableVarName="tableQuotation_Join" msprop:Generator_RowChangedName="Quotation_JoinRowChanged" msprop:Generator_TablePropName="Quotation_Join" msprop:Generator_RowDeletingName="Quotation_JoinRowDeleting" msprop:Generator_RowChangingName="Quotation_JoinRowChanging" msprop:Generator_RowEvHandlerName="Quotation_JoinRowChangeEventHandler" msprop:Generator_RowDeletedName="Quotation_JoinRowDeleted" msprop:Generator_RowClassName="Quotation_JoinRow" msprop:Generator_UserTableName="Quotation_Join" msprop:Generator_RowEvArgName="Quotation_JoinRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="QJ_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnQJ_ID" msprop:Generator_ColumnPropNameInRow="QJ_ID" msprop:Generator_ColumnPropNameInTable="QJ_IDColumn" msprop:Generator_UserColumnName="QJ_ID" type="xs:int" />
              <xs:element name="QuotationID" msprop:Generator_ColumnVarNameInTable="columnQuotationID" msprop:Generator_ColumnPropNameInRow="QuotationID" msprop:Generator_ColumnPropNameInTable="QuotationIDColumn" msprop:Generator_UserColumnName="QuotationID" type="xs:int" />
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="Cost" msprop:Generator_ColumnVarNameInTable="columnCost" msprop:Generator_ColumnPropNameInRow="Cost" msprop:Generator_ColumnPropNameInTable="CostColumn" msprop:Generator_UserColumnName="Cost" type="xs:decimal" />
              <xs:element name="Qty" msprop:Generator_ColumnVarNameInTable="columnQty" msprop:Generator_ColumnPropNameInRow="Qty" msprop:Generator_ColumnPropNameInTable="QtyColumn" msprop:Generator_UserColumnName="Qty" type="xs:int" />
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="DiscountPer" msprop:Generator_ColumnVarNameInTable="columnDiscountPer" msprop:Generator_ColumnPropNameInRow="DiscountPer" msprop:Generator_ColumnPropNameInTable="DiscountPerColumn" msprop:Generator_UserColumnName="DiscountPer" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="VATPer" msprop:Generator_ColumnVarNameInTable="columnVATPer" msprop:Generator_ColumnPropNameInRow="VATPer" msprop:Generator_ColumnPropNameInTable="VATPerColumn" msprop:Generator_UserColumnName="VATPer" type="xs:decimal" />
              <xs:element name="VAT" msprop:Generator_ColumnVarNameInTable="columnVAT" msprop:Generator_ColumnPropNameInRow="VAT" msprop:Generator_ColumnPropNameInTable="VATColumn" msprop:Generator_UserColumnName="VAT" type="xs:decimal" />
              <xs:element name="TotalAmount" msprop:Generator_ColumnVarNameInTable="columnTotalAmount" msprop:Generator_ColumnPropNameInRow="TotalAmount" msprop:Generator_ColumnPropNameInTable="TotalAmountColumn" msprop:Generator_UserColumnName="TotalAmount" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Registration" msprop:Generator_TableClassName="RegistrationDataTable" msprop:Generator_TableVarName="tableRegistration" msprop:Generator_RowChangedName="RegistrationRowChanged" msprop:Generator_TablePropName="Registration" msprop:Generator_RowDeletingName="RegistrationRowDeleting" msprop:Generator_RowChangingName="RegistrationRowChanging" msprop:Generator_RowEvHandlerName="RegistrationRowChangeEventHandler" msprop:Generator_RowDeletedName="RegistrationRowDeleted" msprop:Generator_RowClassName="RegistrationRow" msprop:Generator_UserTableName="Registration" msprop:Generator_RowEvArgName="RegistrationRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="UserID" msprop:Generator_ColumnVarNameInTable="columnUserID" msprop:Generator_ColumnPropNameInRow="UserID" msprop:Generator_ColumnPropNameInTable="UserIDColumn" msprop:Generator_UserColumnName="UserID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="UserType" msprop:Generator_ColumnVarNameInTable="columnUserType" msprop:Generator_ColumnPropNameInRow="UserType" msprop:Generator_ColumnPropNameInTable="UserTypeColumn" msprop:Generator_UserColumnName="UserType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Password" msprop:Generator_ColumnVarNameInTable="columnPassword" msprop:Generator_ColumnPropNameInRow="Password" msprop:Generator_ColumnPropNameInTable="PasswordColumn" msprop:Generator_UserColumnName="Password">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="JoiningDate" msprop:Generator_ColumnVarNameInTable="columnJoiningDate" msprop:Generator_ColumnPropNameInRow="JoiningDate" msprop:Generator_ColumnPropNameInTable="JoiningDateColumn" msprop:Generator_UserColumnName="JoiningDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Active" msprop:Generator_ColumnVarNameInTable="columnActive" msprop:Generator_ColumnPropNameInRow="Active" msprop:Generator_ColumnPropNameInTable="ActiveColumn" msprop:Generator_UserColumnName="Active" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Service" msprop:Generator_TableClassName="ServiceDataTable" msprop:Generator_TableVarName="tableService" msprop:Generator_RowChangedName="ServiceRowChanged" msprop:Generator_TablePropName="Service" msprop:Generator_RowDeletingName="ServiceRowDeleting" msprop:Generator_RowChangingName="ServiceRowChanging" msprop:Generator_RowEvHandlerName="ServiceRowChangeEventHandler" msprop:Generator_RowDeletedName="ServiceRowDeleted" msprop:Generator_RowClassName="ServiceRow" msprop:Generator_UserTableName="Service" msprop:Generator_RowEvArgName="ServiceRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="S_ID" msprop:Generator_ColumnVarNameInTable="columnS_ID" msprop:Generator_ColumnPropNameInRow="S_ID" msprop:Generator_ColumnPropNameInTable="S_IDColumn" msprop:Generator_UserColumnName="S_ID" type="xs:int" />
              <xs:element name="ServiceCode" msprop:Generator_ColumnVarNameInTable="columnServiceCode" msprop:Generator_ColumnPropNameInRow="ServiceCode" msprop:Generator_ColumnPropNameInTable="ServiceCodeColumn" msprop:Generator_UserColumnName="ServiceCode">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CustomerID" msprop:Generator_ColumnVarNameInTable="columnCustomerID" msprop:Generator_ColumnPropNameInRow="CustomerID" msprop:Generator_ColumnPropNameInTable="CustomerIDColumn" msprop:Generator_UserColumnName="CustomerID" type="xs:int" />
              <xs:element name="ServiceType" msprop:Generator_ColumnVarNameInTable="columnServiceType" msprop:Generator_ColumnPropNameInRow="ServiceType" msprop:Generator_ColumnPropNameInTable="ServiceTypeColumn" msprop:Generator_UserColumnName="ServiceType" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ServiceCreationDate" msprop:Generator_ColumnVarNameInTable="columnServiceCreationDate" msprop:Generator_ColumnPropNameInRow="ServiceCreationDate" msprop:Generator_ColumnPropNameInTable="ServiceCreationDateColumn" msprop:Generator_UserColumnName="ServiceCreationDate" type="xs:dateTime" />
              <xs:element name="ItemDescription" msprop:Generator_ColumnVarNameInTable="columnItemDescription" msprop:Generator_ColumnPropNameInRow="ItemDescription" msprop:Generator_ColumnPropNameInTable="ItemDescriptionColumn" msprop:Generator_UserColumnName="ItemDescription">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProblemDescription" msprop:Generator_ColumnVarNameInTable="columnProblemDescription" msprop:Generator_ColumnPropNameInRow="ProblemDescription" msprop:Generator_ColumnPropNameInTable="ProblemDescriptionColumn" msprop:Generator_UserColumnName="ProblemDescription" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChargesQuote" msprop:Generator_ColumnVarNameInTable="columnChargesQuote" msprop:Generator_ColumnPropNameInRow="ChargesQuote" msprop:Generator_ColumnPropNameInTable="ChargesQuoteColumn" msprop:Generator_UserColumnName="ChargesQuote" type="xs:decimal" />
              <xs:element name="AdvanceDeposit" msprop:Generator_ColumnVarNameInTable="columnAdvanceDeposit" msprop:Generator_ColumnPropNameInRow="AdvanceDeposit" msprop:Generator_ColumnPropNameInTable="AdvanceDepositColumn" msprop:Generator_UserColumnName="AdvanceDeposit" type="xs:decimal" />
              <xs:element name="EstimatedRepairDate" msprop:Generator_ColumnVarNameInTable="columnEstimatedRepairDate" msprop:Generator_ColumnPropNameInRow="EstimatedRepairDate" msprop:Generator_ColumnPropNameInTable="EstimatedRepairDateColumn" msprop:Generator_UserColumnName="EstimatedRepairDate" type="xs:dateTime" />
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Status" msprop:Generator_ColumnVarNameInTable="columnStatus" msprop:Generator_ColumnPropNameInRow="Status" msprop:Generator_ColumnPropNameInTable="StatusColumn" msprop:Generator_UserColumnName="Status">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="SMSSetting" msprop:Generator_TableClassName="SMSSettingDataTable" msprop:Generator_TableVarName="tableSMSSetting" msprop:Generator_RowChangedName="SMSSettingRowChanged" msprop:Generator_TablePropName="SMSSetting" msprop:Generator_RowDeletingName="SMSSettingRowDeleting" msprop:Generator_RowChangingName="SMSSettingRowChanging" msprop:Generator_RowEvHandlerName="SMSSettingRowChangeEventHandler" msprop:Generator_RowDeletedName="SMSSettingRowDeleted" msprop:Generator_RowClassName="SMSSettingRow" msprop:Generator_UserTableName="SMSSetting" msprop:Generator_RowEvArgName="SMSSettingRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_UserColumnName="Id" type="xs:int" />
              <xs:element name="APIUrl" msprop:Generator_ColumnVarNameInTable="columnAPIUrl" msprop:Generator_ColumnPropNameInRow="APIUrl" msprop:Generator_ColumnPropNameInTable="APIUrlColumn" msprop:Generator_UserColumnName="APIUrl">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IsDefault" msprop:Generator_ColumnVarNameInTable="columnIsDefault" msprop:Generator_ColumnPropNameInRow="IsDefault" msprop:Generator_ColumnPropNameInTable="IsDefaultColumn" msprop:Generator_UserColumnName="IsDefault">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="IsEnabled" msprop:Generator_ColumnVarNameInTable="columnIsEnabled" msprop:Generator_ColumnPropNameInRow="IsEnabled" msprop:Generator_ColumnPropNameInTable="IsEnabledColumn" msprop:Generator_UserColumnName="IsEnabled">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Stock" msprop:Generator_TableClassName="StockDataTable" msprop:Generator_TableVarName="tableStock" msprop:Generator_RowChangedName="StockRowChanged" msprop:Generator_TablePropName="Stock" msprop:Generator_RowDeletingName="StockRowDeleting" msprop:Generator_RowChangingName="StockRowChanging" msprop:Generator_RowEvHandlerName="StockRowChangeEventHandler" msprop:Generator_RowDeletedName="StockRowDeleted" msprop:Generator_RowClassName="StockRow" msprop:Generator_UserTableName="Stock" msprop:Generator_RowEvArgName="StockRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ST_ID" msprop:Generator_ColumnVarNameInTable="columnST_ID" msprop:Generator_ColumnPropNameInRow="ST_ID" msprop:Generator_ColumnPropNameInTable="ST_IDColumn" msprop:Generator_UserColumnName="ST_ID" type="xs:int" />
              <xs:element name="InvoiceNo" msprop:Generator_ColumnVarNameInTable="columnInvoiceNo" msprop:Generator_ColumnPropNameInRow="InvoiceNo" msprop:Generator_ColumnPropNameInTable="InvoiceNoColumn" msprop:Generator_UserColumnName="InvoiceNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="_Date" msprop:Generator_ColumnPropNameInTable="DateColumn" msprop:Generator_UserColumnName="Date" type="xs:dateTime" />
              <xs:element name="PurchaseType" msprop:Generator_ColumnVarNameInTable="columnPurchaseType" msprop:Generator_ColumnPropNameInRow="PurchaseType" msprop:Generator_ColumnPropNameInTable="PurchaseTypeColumn" msprop:Generator_UserColumnName="PurchaseType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="20" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SupplierID" msprop:Generator_ColumnVarNameInTable="columnSupplierID" msprop:Generator_ColumnPropNameInRow="SupplierID" msprop:Generator_ColumnPropNameInTable="SupplierIDColumn" msprop:Generator_UserColumnName="SupplierID" type="xs:int" />
              <xs:element name="SubTotal" msprop:Generator_ColumnVarNameInTable="columnSubTotal" msprop:Generator_ColumnPropNameInRow="SubTotal" msprop:Generator_ColumnPropNameInTable="SubTotalColumn" msprop:Generator_UserColumnName="SubTotal" type="xs:decimal" />
              <xs:element name="DiscountPer" msprop:Generator_ColumnVarNameInTable="columnDiscountPer" msprop:Generator_ColumnPropNameInRow="DiscountPer" msprop:Generator_ColumnPropNameInTable="DiscountPerColumn" msprop:Generator_UserColumnName="DiscountPer" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="PreviousDue" msprop:Generator_ColumnVarNameInTable="columnPreviousDue" msprop:Generator_ColumnPropNameInRow="PreviousDue" msprop:Generator_ColumnPropNameInTable="PreviousDueColumn" msprop:Generator_UserColumnName="PreviousDue" type="xs:decimal" />
              <xs:element name="FreightCharges" msprop:Generator_ColumnVarNameInTable="columnFreightCharges" msprop:Generator_ColumnPropNameInRow="FreightCharges" msprop:Generator_ColumnPropNameInTable="FreightChargesColumn" msprop:Generator_UserColumnName="FreightCharges" type="xs:decimal" />
              <xs:element name="OtherCharges" msprop:Generator_ColumnVarNameInTable="columnOtherCharges" msprop:Generator_ColumnPropNameInRow="OtherCharges" msprop:Generator_ColumnPropNameInTable="OtherChargesColumn" msprop:Generator_UserColumnName="OtherCharges" type="xs:decimal" />
              <xs:element name="Total" msprop:Generator_ColumnVarNameInTable="columnTotal" msprop:Generator_ColumnPropNameInRow="Total" msprop:Generator_ColumnPropNameInTable="TotalColumn" msprop:Generator_UserColumnName="Total" type="xs:decimal" />
              <xs:element name="RoundOff" msprop:Generator_ColumnVarNameInTable="columnRoundOff" msprop:Generator_ColumnPropNameInRow="RoundOff" msprop:Generator_ColumnPropNameInTable="RoundOffColumn" msprop:Generator_UserColumnName="RoundOff" type="xs:decimal" />
              <xs:element name="GrandTotal" msprop:Generator_ColumnVarNameInTable="columnGrandTotal" msprop:Generator_ColumnPropNameInRow="GrandTotal" msprop:Generator_ColumnPropNameInTable="GrandTotalColumn" msprop:Generator_UserColumnName="GrandTotal" type="xs:decimal" />
              <xs:element name="TotalPayment" msprop:Generator_ColumnVarNameInTable="columnTotalPayment" msprop:Generator_ColumnPropNameInRow="TotalPayment" msprop:Generator_ColumnPropNameInTable="TotalPaymentColumn" msprop:Generator_UserColumnName="TotalPayment" type="xs:decimal" />
              <xs:element name="PaymentDue" msprop:Generator_ColumnVarNameInTable="columnPaymentDue" msprop:Generator_ColumnPropNameInRow="PaymentDue" msprop:Generator_ColumnPropNameInTable="PaymentDueColumn" msprop:Generator_UserColumnName="PaymentDue" type="xs:decimal" />
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Stock_Product" msprop:Generator_TableClassName="Stock_ProductDataTable" msprop:Generator_TableVarName="tableStock_Product" msprop:Generator_RowChangedName="Stock_ProductRowChanged" msprop:Generator_TablePropName="Stock_Product" msprop:Generator_RowDeletingName="Stock_ProductRowDeleting" msprop:Generator_RowChangingName="Stock_ProductRowChanging" msprop:Generator_RowEvHandlerName="Stock_ProductRowChangeEventHandler" msprop:Generator_RowDeletedName="Stock_ProductRowDeleted" msprop:Generator_RowClassName="Stock_ProductRow" msprop:Generator_UserTableName="Stock_Product" msprop:Generator_RowEvArgName="Stock_ProductRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SP_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnSP_ID" msprop:Generator_ColumnPropNameInRow="SP_ID" msprop:Generator_ColumnPropNameInTable="SP_IDColumn" msprop:Generator_UserColumnName="SP_ID" type="xs:int" />
              <xs:element name="StockID" msprop:Generator_ColumnVarNameInTable="columnStockID" msprop:Generator_ColumnPropNameInRow="StockID" msprop:Generator_ColumnPropNameInTable="StockIDColumn" msprop:Generator_UserColumnName="StockID" type="xs:int" />
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="Qty" msprop:Generator_ColumnVarNameInTable="columnQty" msprop:Generator_ColumnPropNameInRow="Qty" msprop:Generator_ColumnPropNameInTable="QtyColumn" msprop:Generator_UserColumnName="Qty" type="xs:int" />
              <xs:element name="Price" msprop:Generator_ColumnVarNameInTable="columnPrice" msprop:Generator_ColumnPropNameInRow="Price" msprop:Generator_ColumnPropNameInTable="PriceColumn" msprop:Generator_UserColumnName="Price" type="xs:decimal" />
              <xs:element name="TotalAmount" msprop:Generator_ColumnVarNameInTable="columnTotalAmount" msprop:Generator_ColumnPropNameInRow="TotalAmount" msprop:Generator_ColumnPropNameInTable="TotalAmountColumn" msprop:Generator_UserColumnName="TotalAmount" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="SubCategory" msprop:Generator_TableClassName="SubCategoryDataTable" msprop:Generator_TableVarName="tableSubCategory" msprop:Generator_RowChangedName="SubCategoryRowChanged" msprop:Generator_TablePropName="SubCategory" msprop:Generator_RowDeletingName="SubCategoryRowDeleting" msprop:Generator_RowChangingName="SubCategoryRowChanging" msprop:Generator_RowEvHandlerName="SubCategoryRowChangeEventHandler" msprop:Generator_RowDeletedName="SubCategoryRowDeleted" msprop:Generator_RowClassName="SubCategoryRow" msprop:Generator_UserTableName="SubCategory" msprop:Generator_RowEvArgName="SubCategoryRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="SubCategoryName" msprop:Generator_ColumnVarNameInTable="columnSubCategoryName" msprop:Generator_ColumnPropNameInRow="SubCategoryName" msprop:Generator_ColumnPropNameInTable="SubCategoryNameColumn" msprop:Generator_UserColumnName="SubCategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Category" msprop:Generator_ColumnVarNameInTable="columnCategory" msprop:Generator_ColumnPropNameInRow="Category" msprop:Generator_ColumnPropNameInTable="CategoryColumn" msprop:Generator_UserColumnName="Category">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Supplier" msprop:Generator_TableClassName="SupplierDataTable" msprop:Generator_TableVarName="tableSupplier" msprop:Generator_RowChangedName="SupplierRowChanged" msprop:Generator_TablePropName="Supplier" msprop:Generator_RowDeletingName="SupplierRowDeleting" msprop:Generator_RowChangingName="SupplierRowChanging" msprop:Generator_RowEvHandlerName="SupplierRowChangeEventHandler" msprop:Generator_RowDeletedName="SupplierRowDeleted" msprop:Generator_RowClassName="SupplierRow" msprop:Generator_UserTableName="Supplier" msprop:Generator_RowEvArgName="SupplierRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="SupplierID" msprop:Generator_ColumnVarNameInTable="columnSupplierID" msprop:Generator_ColumnPropNameInRow="SupplierID" msprop:Generator_ColumnPropNameInTable="SupplierIDColumn" msprop:Generator_UserColumnName="SupplierID">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Address" msprop:Generator_ColumnVarNameInTable="columnAddress" msprop:Generator_ColumnPropNameInRow="Address" msprop:Generator_ColumnPropNameInTable="AddressColumn" msprop:Generator_UserColumnName="Address" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="City" msprop:Generator_ColumnVarNameInTable="columnCity" msprop:Generator_ColumnPropNameInRow="City" msprop:Generator_ColumnPropNameInTable="CityColumn" msprop:Generator_UserColumnName="City" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="State" msprop:Generator_ColumnVarNameInTable="columnState" msprop:Generator_ColumnPropNameInRow="State" msprop:Generator_ColumnPropNameInTable="StateColumn" msprop:Generator_UserColumnName="State" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ZipCode" msprop:Generator_ColumnVarNameInTable="columnZipCode" msprop:Generator_ColumnPropNameInRow="ZipCode" msprop:Generator_ColumnPropNameInTable="ZipCodeColumn" msprop:Generator_UserColumnName="ZipCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="15" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContactNo" msprop:Generator_ColumnVarNameInTable="columnContactNo" msprop:Generator_ColumnPropNameInRow="ContactNo" msprop:Generator_ColumnPropNameInTable="ContactNoColumn" msprop:Generator_UserColumnName="ContactNo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="EmailID" msprop:Generator_ColumnVarNameInTable="columnEmailID" msprop:Generator_ColumnPropNameInRow="EmailID" msprop:Generator_ColumnPropNameInTable="EmailIDColumn" msprop:Generator_UserColumnName="EmailID" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Remarks" msprop:Generator_ColumnVarNameInTable="columnRemarks" msprop:Generator_ColumnPropNameInRow="Remarks" msprop:Generator_ColumnPropNameInTable="RemarksColumn" msprop:Generator_UserColumnName="Remarks" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Temp_Stock" msprop:Generator_TableClassName="Temp_StockDataTable" msprop:Generator_TableVarName="tableTemp_Stock" msprop:Generator_RowChangedName="Temp_StockRowChanged" msprop:Generator_TablePropName="Temp_Stock" msprop:Generator_RowDeletingName="Temp_StockRowDeleting" msprop:Generator_RowChangingName="Temp_StockRowChanging" msprop:Generator_RowEvHandlerName="Temp_StockRowChangeEventHandler" msprop:Generator_RowDeletedName="Temp_StockRowDeleted" msprop:Generator_RowClassName="Temp_StockRow" msprop:Generator_UserTableName="Temp_Stock" msprop:Generator_RowEvArgName="Temp_StockRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ProductID" msprop:Generator_ColumnVarNameInTable="columnProductID" msprop:Generator_ColumnPropNameInRow="ProductID" msprop:Generator_ColumnPropNameInTable="ProductIDColumn" msprop:Generator_UserColumnName="ProductID" type="xs:int" />
              <xs:element name="Qty" msprop:Generator_ColumnVarNameInTable="columnQty" msprop:Generator_ColumnPropNameInRow="Qty" msprop:Generator_ColumnPropNameInTable="QtyColumn" msprop:Generator_UserColumnName="Qty" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Voucher" msprop:Generator_TableClassName="VoucherDataTable" msprop:Generator_TableVarName="tableVoucher" msprop:Generator_RowChangedName="VoucherRowChanged" msprop:Generator_TablePropName="Voucher" msprop:Generator_RowDeletingName="VoucherRowDeleting" msprop:Generator_RowChangingName="VoucherRowChanging" msprop:Generator_RowEvHandlerName="VoucherRowChangeEventHandler" msprop:Generator_RowDeletedName="VoucherRowDeleted" msprop:Generator_RowClassName="VoucherRow" msprop:Generator_UserTableName="Voucher" msprop:Generator_RowEvArgName="VoucherRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Id" msprop:Generator_ColumnVarNameInTable="columnId" msprop:Generator_ColumnPropNameInRow="Id" msprop:Generator_ColumnPropNameInTable="IdColumn" msprop:Generator_UserColumnName="Id" type="xs:int" />
              <xs:element name="VoucherNo" msprop:Generator_ColumnVarNameInTable="columnVoucherNo" msprop:Generator_ColumnPropNameInRow="VoucherNo" msprop:Generator_ColumnPropNameInTable="VoucherNoColumn" msprop:Generator_UserColumnName="VoucherNo">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="30" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Name" msprop:Generator_ColumnVarNameInTable="columnName" msprop:Generator_ColumnPropNameInRow="Name" msprop:Generator_ColumnPropNameInTable="NameColumn" msprop:Generator_UserColumnName="Name" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Date" msprop:Generator_ColumnVarNameInTable="columnDate" msprop:Generator_ColumnPropNameInRow="_Date" msprop:Generator_ColumnPropNameInTable="DateColumn" msprop:Generator_UserColumnName="Date" type="xs:dateTime" />
              <xs:element name="Details" msprop:Generator_ColumnVarNameInTable="columnDetails" msprop:Generator_ColumnPropNameInRow="Details" msprop:Generator_ColumnPropNameInTable="DetailsColumn" msprop:Generator_UserColumnName="Details" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GrandTotal" msprop:Generator_ColumnVarNameInTable="columnGrandTotal" msprop:Generator_ColumnPropNameInRow="GrandTotal" msprop:Generator_ColumnPropNameInTable="GrandTotalColumn" msprop:Generator_UserColumnName="GrandTotal" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Voucher_OtherDetails" msprop:Generator_TableClassName="Voucher_OtherDetailsDataTable" msprop:Generator_TableVarName="tableVoucher_OtherDetails" msprop:Generator_RowChangedName="Voucher_OtherDetailsRowChanged" msprop:Generator_TablePropName="Voucher_OtherDetails" msprop:Generator_RowDeletingName="Voucher_OtherDetailsRowDeleting" msprop:Generator_RowChangingName="Voucher_OtherDetailsRowChanging" msprop:Generator_RowEvHandlerName="Voucher_OtherDetailsRowChangeEventHandler" msprop:Generator_RowDeletedName="Voucher_OtherDetailsRowDeleted" msprop:Generator_RowClassName="Voucher_OtherDetailsRow" msprop:Generator_UserTableName="Voucher_OtherDetails" msprop:Generator_RowEvArgName="Voucher_OtherDetailsRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="VD_ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnVD_ID" msprop:Generator_ColumnPropNameInRow="VD_ID" msprop:Generator_ColumnPropNameInTable="VD_IDColumn" msprop:Generator_UserColumnName="VD_ID" type="xs:int" />
              <xs:element name="VoucherID" msprop:Generator_ColumnVarNameInTable="columnVoucherID" msprop:Generator_ColumnPropNameInRow="VoucherID" msprop:Generator_ColumnPropNameInTable="VoucherIDColumn" msprop:Generator_UserColumnName="VoucherID" type="xs:int" />
              <xs:element name="Particulars" msprop:Generator_ColumnVarNameInTable="columnParticulars" msprop:Generator_ColumnPropNameInRow="Particulars" msprop:Generator_ColumnPropNameInTable="ParticularsColumn" msprop:Generator_UserColumnName="Particulars">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="Note" msprop:Generator_ColumnVarNameInTable="columnNote" msprop:Generator_ColumnPropNameInRow="Note" msprop:Generator_ColumnPropNameInTable="NoteColumn" msprop:Generator_UserColumnName="Note" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Activation" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Category_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Category" />
      <xs:field xpath="mstns:CategoryName" />
    </xs:unique>
    <xs:unique name="Company_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Company" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Company_Contacts_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Company_Contacts" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:unique name="Customer_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Customer" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Invoice_Payment_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Invoice_Payment" />
      <xs:field xpath="mstns:IP_ID" />
    </xs:unique>
    <xs:unique name="Invoice_Product_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Invoice_Product" />
      <xs:field xpath="mstns:IPo_ID" />
    </xs:unique>
    <xs:unique name="Invoice1_Payment_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Invoice1_Payment" />
      <xs:field xpath="mstns:IP_ID" />
    </xs:unique>
    <xs:unique name="Invoice1_Product_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Invoice1_Product" />
      <xs:field xpath="mstns:Ipo_ID" />
    </xs:unique>
    <xs:unique name="InvoiceInfo_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:InvoiceInfo" />
      <xs:field xpath="mstns:Inv_ID" />
    </xs:unique>
    <xs:unique name="InvoiceInfo1_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:InvoiceInfo1" />
      <xs:field xpath="mstns:Inv_ID" />
    </xs:unique>
    <xs:unique name="Logs_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Logs" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Product_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Product" />
      <xs:field xpath="mstns:PID" />
    </xs:unique>
    <xs:unique name="Product_Join_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Product_Join" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:unique name="Quotation_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Quotation" />
      <xs:field xpath="mstns:Q_ID" />
    </xs:unique>
    <xs:unique name="Quotation_Join_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Quotation_Join" />
      <xs:field xpath="mstns:QJ_ID" />
    </xs:unique>
    <xs:unique name="Registration_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Registration" />
      <xs:field xpath="mstns:UserID" />
    </xs:unique>
    <xs:unique name="Service_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Service" />
      <xs:field xpath="mstns:S_ID" />
    </xs:unique>
    <xs:unique name="SMSSetting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:SMSSetting" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:unique name="Stock_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Stock" />
      <xs:field xpath="mstns:ST_ID" />
    </xs:unique>
    <xs:unique name="Stock_Product_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Stock_Product" />
      <xs:field xpath="mstns:SP_ID" />
    </xs:unique>
    <xs:unique name="SubCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:SubCategory" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Supplier_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Supplier" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="Temp_Stock_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Temp_Stock" />
      <xs:field xpath="mstns:ProductID" />
    </xs:unique>
    <xs:unique name="Voucher_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Voucher" />
      <xs:field xpath="mstns:Id" />
    </xs:unique>
    <xs:unique name="Voucher_OtherDetails_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Voucher_OtherDetails" />
      <xs:field xpath="mstns:VD_ID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_Invoice_Payment_InvoiceInfo" msdata:parent="InvoiceInfo" msdata:child="Invoice_Payment" msdata:parentkey="Inv_ID" msdata:childkey="InvoiceID" msprop:Generator_UserChildTable="Invoice_Payment" msprop:Generator_ChildPropName="GetInvoice_PaymentRows" msprop:Generator_UserRelationName="FK_Invoice_Payment_InvoiceInfo" msprop:Generator_ParentPropName="InvoiceInfoRow" msprop:Generator_RelationVarName="relationFK_Invoice_Payment_InvoiceInfo" msprop:Generator_UserParentTable="InvoiceInfo" />
      <msdata:Relationship name="FK_Invoice_Product_InvoiceInfo" msdata:parent="InvoiceInfo" msdata:child="Invoice_Product" msdata:parentkey="Inv_ID" msdata:childkey="InvoiceID" msprop:Generator_UserChildTable="Invoice_Product" msprop:Generator_ChildPropName="GetInvoice_ProductRows" msprop:Generator_UserRelationName="FK_Invoice_Product_InvoiceInfo" msprop:Generator_ParentPropName="InvoiceInfoRow" msprop:Generator_RelationVarName="relationFK_Invoice_Product_InvoiceInfo" msprop:Generator_UserParentTable="InvoiceInfo" />
      <msdata:Relationship name="FK_Invoice_Product_Product" msdata:parent="Product" msdata:child="Invoice_Product" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Invoice_Product" msprop:Generator_ChildPropName="GetInvoice_ProductRows" msprop:Generator_UserRelationName="FK_Invoice_Product_Product" msprop:Generator_ParentPropName="ProductRow" msprop:Generator_RelationVarName="relationFK_Invoice_Product_Product" msprop:Generator_UserParentTable="Product" />
      <msdata:Relationship name="FK_Invoice1_Payment_InvoiceInfo1" msdata:parent="InvoiceInfo1" msdata:child="Invoice1_Payment" msdata:parentkey="Inv_ID" msdata:childkey="InvoiceID" msprop:Generator_UserChildTable="Invoice1_Payment" msprop:Generator_ChildPropName="GetInvoice1_PaymentRows" msprop:Generator_UserRelationName="FK_Invoice1_Payment_InvoiceInfo1" msprop:Generator_ParentPropName="InvoiceInfo1Row" msprop:Generator_RelationVarName="relationFK_Invoice1_Payment_InvoiceInfo1" msprop:Generator_UserParentTable="InvoiceInfo1" />
      <msdata:Relationship name="FK_Invoice1_Product_InvoiceInfo1" msdata:parent="InvoiceInfo1" msdata:child="Invoice1_Product" msdata:parentkey="Inv_ID" msdata:childkey="InvoiceID" msprop:Generator_UserChildTable="Invoice1_Product" msprop:Generator_ChildPropName="GetInvoice1_ProductRows" msprop:Generator_UserRelationName="FK_Invoice1_Product_InvoiceInfo1" msprop:Generator_ParentPropName="InvoiceInfo1Row" msprop:Generator_RelationVarName="relationFK_Invoice1_Product_InvoiceInfo1" msprop:Generator_UserParentTable="InvoiceInfo1" />
      <msdata:Relationship name="FK_Invoice1_Product_Product" msdata:parent="Product" msdata:child="Invoice1_Product" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Invoice1_Product" msprop:Generator_ChildPropName="GetInvoice1_ProductRows" msprop:Generator_UserRelationName="FK_Invoice1_Product_Product" msprop:Generator_ParentPropName="ProductRow" msprop:Generator_RelationVarName="relationFK_Invoice1_Product_Product" msprop:Generator_UserParentTable="Product" />
      <msdata:Relationship name="FK_InvoiceInfo_Customer" msdata:parent="Customer" msdata:child="InvoiceInfo" msdata:parentkey="ID" msdata:childkey="CustomerID" msprop:Generator_UserChildTable="InvoiceInfo" msprop:Generator_ChildPropName="GetInvoiceInfoRows" msprop:Generator_UserRelationName="FK_InvoiceInfo_Customer" msprop:Generator_RelationVarName="relationFK_InvoiceInfo_Customer" msprop:Generator_UserParentTable="Customer" msprop:Generator_ParentPropName="CustomerRow" />
      <msdata:Relationship name="FK_InvoiceInfo1_Service" msdata:parent="Service" msdata:child="InvoiceInfo1" msdata:parentkey="S_ID" msdata:childkey="ServiceID" msprop:Generator_UserChildTable="InvoiceInfo1" msprop:Generator_ChildPropName="GetInvoiceInfo1Rows" msprop:Generator_UserRelationName="FK_InvoiceInfo1_Service" msprop:Generator_ParentPropName="ServiceRow" msprop:Generator_RelationVarName="relationFK_InvoiceInfo1_Service" msprop:Generator_UserParentTable="Service" />
      <msdata:Relationship name="FK_Logs_Registration" msdata:parent="Registration" msdata:child="Logs" msdata:parentkey="UserID" msdata:childkey="UserID" msprop:Generator_UserChildTable="Logs" msprop:Generator_ChildPropName="GetLogsRows" msprop:Generator_UserRelationName="FK_Logs_Registration" msprop:Generator_ParentPropName="RegistrationRow" msprop:Generator_RelationVarName="relationFK_Logs_Registration" msprop:Generator_UserParentTable="Registration" />
      <msdata:Relationship name="FK_Product_SubCategory" msdata:parent="SubCategory" msdata:child="Product" msdata:parentkey="ID" msdata:childkey="SubCategoryID" msprop:Generator_UserChildTable="Product" msprop:Generator_ChildPropName="GetProductRows" msprop:Generator_UserRelationName="FK_Product_SubCategory" msprop:Generator_ParentPropName="SubCategoryRow" msprop:Generator_RelationVarName="relationFK_Product_SubCategory" msprop:Generator_UserParentTable="SubCategory" />
      <msdata:Relationship name="FK_Product_Join_Product" msdata:parent="Product" msdata:child="Product_Join" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Product_Join" msprop:Generator_ChildPropName="GetProduct_JoinRows" msprop:Generator_UserRelationName="FK_Product_Join_Product" msprop:Generator_RelationVarName="relationFK_Product_Join_Product" msprop:Generator_UserParentTable="Product" msprop:Generator_ParentPropName="ProductRow" />
      <msdata:Relationship name="FK_Quotation_Customer" msdata:parent="Customer" msdata:child="Quotation" msdata:parentkey="ID" msdata:childkey="CustomerID" msprop:Generator_UserChildTable="Quotation" msprop:Generator_ChildPropName="GetQuotationRows" msprop:Generator_UserRelationName="FK_Quotation_Customer" msprop:Generator_RelationVarName="relationFK_Quotation_Customer" msprop:Generator_UserParentTable="Customer" msprop:Generator_ParentPropName="CustomerRow" />
      <msdata:Relationship name="FK_Quotation_Join_Product" msdata:parent="Product" msdata:child="Quotation_Join" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Quotation_Join" msprop:Generator_ChildPropName="GetQuotation_JoinRows" msprop:Generator_UserRelationName="FK_Quotation_Join_Product" msprop:Generator_RelationVarName="relationFK_Quotation_Join_Product" msprop:Generator_UserParentTable="Product" msprop:Generator_ParentPropName="ProductRow" />
      <msdata:Relationship name="FK_Quotation_Join_Quotation" msdata:parent="Quotation" msdata:child="Quotation_Join" msdata:parentkey="Q_ID" msdata:childkey="QuotationID" msprop:Generator_UserChildTable="Quotation_Join" msprop:Generator_ChildPropName="GetQuotation_JoinRows" msprop:Generator_UserRelationName="FK_Quotation_Join_Quotation" msprop:Generator_RelationVarName="relationFK_Quotation_Join_Quotation" msprop:Generator_UserParentTable="Quotation" msprop:Generator_ParentPropName="QuotationRow" />
      <msdata:Relationship name="FK_Service_Customer" msdata:parent="Customer" msdata:child="Service" msdata:parentkey="ID" msdata:childkey="CustomerID" msprop:Generator_UserChildTable="Service" msprop:Generator_ChildPropName="GetServiceRows" msprop:Generator_UserRelationName="FK_Service_Customer" msprop:Generator_RelationVarName="relationFK_Service_Customer" msprop:Generator_UserParentTable="Customer" msprop:Generator_ParentPropName="CustomerRow" />
      <msdata:Relationship name="FK_Stock_Supplier" msdata:parent="Supplier" msdata:child="Stock" msdata:parentkey="ID" msdata:childkey="SupplierID" msprop:Generator_UserChildTable="Stock" msprop:Generator_ChildPropName="GetStockRows" msprop:Generator_UserRelationName="FK_Stock_Supplier" msprop:Generator_ParentPropName="SupplierRow" msprop:Generator_RelationVarName="relationFK_Stock_Supplier" msprop:Generator_UserParentTable="Supplier" />
      <msdata:Relationship name="FK_Stock_Product_Product" msdata:parent="Product" msdata:child="Stock_Product" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Stock_Product" msprop:Generator_ChildPropName="GetStock_ProductRows" msprop:Generator_UserRelationName="FK_Stock_Product_Product" msprop:Generator_RelationVarName="relationFK_Stock_Product_Product" msprop:Generator_UserParentTable="Product" msprop:Generator_ParentPropName="ProductRow" />
      <msdata:Relationship name="FK_Stock_Product_Stock" msdata:parent="Stock" msdata:child="Stock_Product" msdata:parentkey="ST_ID" msdata:childkey="StockID" msprop:Generator_UserChildTable="Stock_Product" msprop:Generator_ChildPropName="GetStock_ProductRows" msprop:Generator_UserRelationName="FK_Stock_Product_Stock" msprop:Generator_RelationVarName="relationFK_Stock_Product_Stock" msprop:Generator_UserParentTable="Stock" msprop:Generator_ParentPropName="StockRow" />
      <msdata:Relationship name="FK_SubCategory_Category" msdata:parent="Category" msdata:child="SubCategory" msdata:parentkey="CategoryName" msdata:childkey="Category" msprop:Generator_UserChildTable="SubCategory" msprop:Generator_ChildPropName="GetSubCategoryRows" msprop:Generator_UserRelationName="FK_SubCategory_Category" msprop:Generator_RelationVarName="relationFK_SubCategory_Category" msprop:Generator_UserParentTable="Category" msprop:Generator_ParentPropName="CategoryRow" />
      <msdata:Relationship name="FK_Temp_Stock_Product" msdata:parent="Product" msdata:child="Temp_Stock" msdata:parentkey="PID" msdata:childkey="ProductID" msprop:Generator_UserChildTable="Temp_Stock" msprop:Generator_ChildPropName="GetTemp_StockRows" msprop:Generator_UserRelationName="FK_Temp_Stock_Product" msprop:Generator_RelationVarName="relationFK_Temp_Stock_Product" msprop:Generator_UserParentTable="Product" msprop:Generator_ParentPropName="ProductRow" />
      <msdata:Relationship name="FK_Voucher_OtherDetails_Voucher" msdata:parent="Voucher" msdata:child="Voucher_OtherDetails" msdata:parentkey="Id" msdata:childkey="VoucherID" msprop:Generator_UserChildTable="Voucher_OtherDetails" msprop:Generator_ChildPropName="GetVoucher_OtherDetailsRows" msprop:Generator_UserRelationName="FK_Voucher_OtherDetails_Voucher" msprop:Generator_RelationVarName="relationFK_Voucher_OtherDetails_Voucher" msprop:Generator_UserParentTable="Voucher" msprop:Generator_ParentPropName="VoucherRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>