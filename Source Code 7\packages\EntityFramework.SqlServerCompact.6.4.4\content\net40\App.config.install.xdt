﻿<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <entityFramework>
    <defaultConnectionFactory xdt:Transform="Remove" />
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlCeConnectionFactory, EntityFramework" xdt:Transform="Insert">
      <parameters>
        <parameter value="System.Data.SqlServerCe.4.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlServerCe.4.0" type="System.Data.Entity.SqlServerCompact.SqlCeProviderServices, EntityFramework.SqlServerCompact" xdt:Transform="InsertIfMissing" xdt:Locator="Match(invariantName)" />
    </providers>
  </entityFramework>
</configuration>
