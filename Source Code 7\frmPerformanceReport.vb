Imports System.IO
Imports System.Threading.Tasks
Imports System.Windows.Forms

Public Class frmPerformanceReport
    Private analyzer As New PerformanceAnalyzer()
    Private systemChecker As New SystemHealthChecker()
    
    Private Sub frmPerformanceReport_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        InitializeForm()
    End Sub
    
    Private Sub InitializeForm()
        Me.Text = "تقرير فحص أداء النظام"
        Me.Size = New Size(900, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.Icon = My.Resources.App ' استخدام أيقونة التطبيق
        
        ' إنشاء العناصر
        CreateControls()
    End Sub
    
    Private Sub CreateControls()
        ' Panel علوي للأزرار
        Dim topPanel As New Panel()
        topPanel.Dock = DockStyle.Top
        topPanel.Height = 60
        topPanel.BackColor = Color.LightGray
        Me.Controls.Add(topPanel)
        
        ' زر بدء الفحص
        Dim btnStartAnalysis As New Button()
        btnStartAnalysis.Text = "فحص الأداء"
        btnStartAnalysis.Size = New Size(100, 35)
        btnStartAnalysis.Location = New Point(20, 12)
        btnStartAnalysis.BackColor = Color.Green
        btnStartAnalysis.ForeColor = Color.White
        btnStartAnalysis.Font = New Font("Arial", 9, FontStyle.Bold)
        AddHandler btnStartAnalysis.Click, AddressOf BtnStartAnalysis_Click
        topPanel.Controls.Add(btnStartAnalysis)

        ' زر الفحص الشامل
        Dim btnCompleteAnalysis As New Button()
        btnCompleteAnalysis.Text = "فحص شامل"
        btnCompleteAnalysis.Size = New Size(100, 35)
        btnCompleteAnalysis.Location = New Point(130, 12)
        btnCompleteAnalysis.BackColor = Color.DarkBlue
        btnCompleteAnalysis.ForeColor = Color.White
        btnCompleteAnalysis.Font = New Font("Arial", 9, FontStyle.Bold)
        AddHandler btnCompleteAnalysis.Click, AddressOf BtnCompleteAnalysis_Click
        topPanel.Controls.Add(btnCompleteAnalysis)
        
        ' زر حفظ التقرير
        Dim btnSaveReport As New Button()
        btnSaveReport.Text = "حفظ التقرير"
        btnSaveReport.Size = New Size(100, 35)
        btnSaveReport.Location = New Point(240, 12)
        btnSaveReport.BackColor = Color.Blue
        btnSaveReport.ForeColor = Color.White
        btnSaveReport.Font = New Font("Arial", 10, FontStyle.Bold)
        AddHandler btnSaveReport.Click, AddressOf BtnSaveReport_Click
        topPanel.Controls.Add(btnSaveReport)
        
        ' زر طباعة التقرير
        Dim btnPrintReport As New Button()
        btnPrintReport.Text = "طباعة"
        btnPrintReport.Size = New Size(80, 35)
        btnPrintReport.Location = New Point(350, 12)
        btnPrintReport.BackColor = Color.Orange
        btnPrintReport.ForeColor = Color.White
        btnSaveReport.Font = New Font("Arial", 10, FontStyle.Bold)
        AddHandler btnPrintReport.Click, AddressOf BtnPrintReport_Click
        topPanel.Controls.Add(btnPrintReport)
        
        ' شريط التقدم
        Dim progressBar As New ProgressBar()
        progressBar.Name = "progressBar"
        progressBar.Size = New Size(200, 25)
        progressBar.Location = New Point(400, 17)
        progressBar.Style = ProgressBarStyle.Marquee
        progressBar.Visible = False
        topPanel.Controls.Add(progressBar)
        
        ' تسمية الحالة
        Dim lblStatus As New Label()
        lblStatus.Name = "lblStatus"
        lblStatus.Text = "جاهز لبدء الفحص"
        lblStatus.Size = New Size(150, 25)
        lblStatus.Location = New Point(620, 20)
        lblStatus.Font = New Font("Arial", 9)
        lblStatus.ForeColor = Color.DarkBlue
        topPanel.Controls.Add(lblStatus)
        
        ' مربع النص لعرض التقرير
        Dim txtReport As New TextBox()
        txtReport.Name = "txtReport"
        txtReport.Multiline = True
        txtReport.ScrollBars = ScrollBars.Both
        txtReport.Dock = DockStyle.Fill
        txtReport.Font = New Font("Courier New", 9)
        txtReport.BackColor = Color.White
        txtReport.ReadOnly = True
        txtReport.Text = "اضغط على 'بدء فحص الأداء' لبدء التحليل..."
        Me.Controls.Add(txtReport)
        
        ' Panel سفلي للمعلومات
        Dim bottomPanel As New Panel()
        bottomPanel.Dock = DockStyle.Bottom
        bottomPanel.Height = 30
        bottomPanel.BackColor = Color.LightGray
        Me.Controls.Add(bottomPanel)
        
        Dim lblInfo As New Label()
        lblInfo.Text = "نظام فحص الأداء - إصدار 1.0"
        lblInfo.Location = New Point(10, 5)
        lblInfo.Size = New Size(200, 20)
        lblInfo.Font = New Font("Arial", 8)
        bottomPanel.Controls.Add(lblInfo)
        
        Dim lblTime As New Label()
        lblTime.Name = "lblTime"
        lblTime.Text = ""
        lblTime.Location = New Point(700, 5)
        lblTime.Size = New Size(150, 20)
        lblTime.Font = New Font("Arial", 8)
        lblTime.TextAlign = ContentAlignment.MiddleRight
        bottomPanel.Controls.Add(lblTime)
    End Sub
    
    Private Async Sub BtnCompleteAnalysis_Click(sender As Object, e As EventArgs)
        Dim btnComplete As Button = DirectCast(sender, Button)
        Dim progressBar As ProgressBar = DirectCast(Me.Controls.Find("progressBar", True)(0), ProgressBar)
        Dim lblStatus As Label = DirectCast(Me.Controls.Find("lblStatus", True)(0), Label)
        Dim txtReport As TextBox = DirectCast(Me.Controls.Find("txtReport", True)(0), TextBox)
        Dim lblTime As Label = DirectCast(Me.Controls.Find("lblTime", True)(0), Label)

        Try
            ' تعطيل الأزرار وإظهار شريط التقدم
            btnComplete.Enabled = False
            progressBar.Visible = True
            lblStatus.Text = "جاري الفحص الشامل..."
            txtReport.Text = "جاري تحليل النظام بشكل شامل، يرجى الانتظار..." & vbCrLf & "هذا قد يستغرق عدة دقائق..."

            Dim startTime As DateTime = DateTime.Now

            ' تشغيل التحليل الشامل في مهمة منفصلة
            Dim report As String = Await Task.Run(Function() systemChecker.GenerateCompleteReport())

            Dim endTime As DateTime = DateTime.Now
            Dim duration As TimeSpan = endTime - startTime

            ' عرض النتائج
            txtReport.Text = report
            lblStatus.Text = "تم الانتهاء من الفحص الشامل"
            lblTime.Text = $"وقت الفحص: {duration.TotalSeconds:F2} ثانية"

        Catch ex As Exception
            txtReport.Text = $"حدث خطأ أثناء الفحص الشامل:{vbCrLf}{ex.Message}{vbCrLf}{vbCrLf}تفاصيل الخطأ:{vbCrLf}{ex.StackTrace}"
            lblStatus.Text = "حدث خطأ في الفحص الشامل"
            MessageBox.Show($"حدث خطأ أثناء الفحص الشامل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)

        Finally
            ' إعادة تفعيل الزر وإخفاء شريط التقدم
            btnComplete.Enabled = True
            progressBar.Visible = False
        End Try
    End Sub

    Private Async Sub BtnStartAnalysis_Click(sender As Object, e As EventArgs)
        Dim btnStart As Button = DirectCast(sender, Button)
        Dim progressBar As ProgressBar = DirectCast(Me.Controls.Find("progressBar", True)(0), ProgressBar)
        Dim lblStatus As Label = DirectCast(Me.Controls.Find("lblStatus", True)(0), Label)
        Dim txtReport As TextBox = DirectCast(Me.Controls.Find("txtReport", True)(0), TextBox)
        Dim lblTime As Label = DirectCast(Me.Controls.Find("lblTime", True)(0), Label)
        
        Try
            ' تعطيل الزر وإظهار شريط التقدم
            btnStart.Enabled = False
            progressBar.Visible = True
            lblStatus.Text = "جاري فحص الأداء..."
            txtReport.Text = "جاري تحليل النظام، يرجى الانتظار..."
            
            Dim startTime As DateTime = DateTime.Now
            
            ' تشغيل التحليل في مهمة منفصلة
            Dim report As String = Await Task.Run(Function() analyzer.AnalyzePerformance())
            
            Dim endTime As DateTime = DateTime.Now
            Dim duration As TimeSpan = endTime - startTime
            
            ' عرض النتائج
            txtReport.Text = report
            lblStatus.Text = "تم الانتهاء من الفحص"
            lblTime.Text = $"وقت الفحص: {duration.TotalSeconds:F2} ثانية"
            
        Catch ex As Exception
            txtReport.Text = $"حدث خطأ أثناء فحص الأداء:{vbCrLf}{ex.Message}{vbCrLf}{vbCrLf}تفاصيل الخطأ:{vbCrLf}{ex.StackTrace}"
            lblStatus.Text = "حدث خطأ في الفحص"
            MessageBox.Show($"حدث خطأ أثناء فحص الأداء: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
            
        Finally
            ' إعادة تفعيل الزر وإخفاء شريط التقدم
            btnStart.Enabled = True
            progressBar.Visible = False
        End Try
    End Sub
    
    Private Sub BtnSaveReport_Click(sender As Object, e As EventArgs)
        Dim txtReport As TextBox = DirectCast(Me.Controls.Find("txtReport", True)(0), TextBox)
        
        If String.IsNullOrEmpty(txtReport.Text) OrElse txtReport.Text.Contains("اضغط على") Then
            MessageBox.Show("لا يوجد تقرير لحفظه. يرجى تشغيل فحص الأداء أولاً.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        Try
            Dim saveDialog As New SaveFileDialog()
            saveDialog.Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*"
            saveDialog.FileName = $"Performance_Report_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            saveDialog.Title = "حفظ تقرير فحص الأداء"
            
            If saveDialog.ShowDialog() = DialogResult.OK Then
                File.WriteAllText(saveDialog.FileName, txtReport.Text, System.Text.Encoding.UTF8)
                MessageBox.Show($"تم حفظ التقرير بنجاح في:{vbCrLf}{saveDialog.FileName}", "نجح الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ أثناء حفظ التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub BtnPrintReport_Click(sender As Object, e As EventArgs)
        Dim txtReport As TextBox = DirectCast(Me.Controls.Find("txtReport", True)(0), TextBox)
        
        If String.IsNullOrEmpty(txtReport.Text) OrElse txtReport.Text.Contains("اضغط على") Then
            MessageBox.Show("لا يوجد تقرير للطباعة. يرجى تشغيل فحص الأداء أولاً.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        End If
        
        Try
            Dim printDialog As New PrintDialog()
            Dim printDocument As New Printing.PrintDocument()
            
            AddHandler printDocument.PrintPage, Sub(s, ev)
                                                    Dim font As New Font("Arial", 10)
                                                    Dim brush As New SolidBrush(Color.Black)
                                                    ev.Graphics.DrawString(txtReport.Text, font, brush, ev.MarginBounds)
                                                End Sub
            
            printDialog.Document = printDocument
            
            If printDialog.ShowDialog() = DialogResult.OK Then
                printDocument.Print()
                MessageBox.Show("تم إرسال التقرير للطباعة بنجاح.", "نجحت الطباعة", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
        Catch ex As Exception
            MessageBox.Show($"حدث خطأ أثناء طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub frmPerformanceReport_KeyDown(sender As Object, e As KeyEventArgs) Handles MyBase.KeyDown
        ' اختصارات لوحة المفاتيح
        If e.Control Then
            Select Case e.KeyCode
                Case Keys.S ' Ctrl+S للحفظ
                    BtnSaveReport_Click(Nothing, Nothing)
                Case Keys.P ' Ctrl+P للطباعة
                    BtnPrintReport_Click(Nothing, Nothing)
                Case Keys.F5 ' Ctrl+F5 لبدء الفحص
                    BtnStartAnalysis_Click(Me.Controls.Find("btnStartAnalysis", True)(0), Nothing)
            End Select
        End If
        
        If e.KeyCode = Keys.Escape Then
            Me.Close()
        End If
    End Sub
End Class
