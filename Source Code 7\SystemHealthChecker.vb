Imports System.Text
Imports System.IO

Public Class SystemHealthChecker
    Private performanceAnalyzer As New PerformanceAnalyzer()
    Private codeAnalyzer As New CodeAnalyzer()
    
    Public Function GenerateCompleteReport() As String
        Dim report As New StringBuilder()
        
        report.AppendLine("████████████████████████████████████████████████████████████████")
        report.AppendLine("█                                                              █")
        report.AppendLine("█           تقرير فحص شامل لنظام المبيعات والمخزون           █")
        report.AppendLine("█                                                              █")
        report.AppendLine("████████████████████████████████████████████████████████████████")
        report.AppendLine()
        report.AppendLine($"تاريخ الفحص: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
        report.AppendLine($"نظام التشغيل: {Environment.OSVersion}")
        report.AppendLine($"إصدار .NET Framework: {Environment.Version}")
        report.AppendLine($"اسم الجهاز: {Environment.MachineName}")
        report.AppendLine($"اسم المستخدم: {Environment.UserName}")
        report.AppendLine()
        
        ' معلومات عامة عن التطبيق
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        report.AppendLine("                    معلومات عامة عن التطبيق")
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        
        Try
            Dim appPath As String = Application.StartupPath
            report.AppendLine($"مسار التطبيق: {appPath}")
            
            ' فحص الملفات الأساسية
            CheckEssentialFiles(appPath, report)
            
            ' معلومات المشروع
            GetProjectInfo(appPath, report)
            
        Catch ex As Exception
            report.AppendLine($"خطأ في جمع المعلومات العامة: {ex.Message}")
        End Try
        
        ' تحليل الأداء
        report.AppendLine(vbCrLf & "═══════════════════════════════════════════════════════════════")
        report.AppendLine("                        تحليل الأداء")
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        
        Try
            Dim performanceReport As String = performanceAnalyzer.AnalyzePerformance()
            report.AppendLine(performanceReport)
        Catch ex As Exception
            report.AppendLine($"خطأ في تحليل الأداء: {ex.Message}")
        End Try
        
        ' تحليل الكود
        report.AppendLine(vbCrLf & "═══════════════════════════════════════════════════════════════")
        report.AppendLine("                        تحليل جودة الكود")
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        
        Try
            Dim codeReport As String = codeAnalyzer.AnalyzeCode(Application.StartupPath)
            report.AppendLine(codeReport)
        Catch ex As Exception
            report.AppendLine($"خطأ في تحليل الكود: {ex.Message}")
        End Try
        
        ' تقييم عام
        report.AppendLine(vbCrLf & "═══════════════════════════════════════════════════════════════")
        report.AppendLine("                        التقييم العام")
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        
        GenerateOverallAssessment(report)
        
        ' خطة العمل المقترحة
        report.AppendLine(vbCrLf & "═══════════════════════════════════════════════════════════════")
        report.AppendLine("                      خطة العمل المقترحة")
        report.AppendLine("═══════════════════════════════════════════════════════════════")
        
        GenerateActionPlan(report)
        
        report.AppendLine(vbCrLf & "████████████████████████████████████████████████████████████████")
        report.AppendLine("█                      انتهى التقرير                         █")
        report.AppendLine("████████████████████████████████████████████████████████████████")
        
        Return report.ToString()
    End Function
    
    Private Sub CheckEssentialFiles(appPath As String, report As StringBuilder)
        report.AppendLine(vbCrLf & "الملفات الأساسية:")
        
        Dim essentialFiles As String() = {
            "Sales_and_Inventory_System.exe",
            "SQLSettings.dat",
            "App.config"
        }
        
        For Each fileName As String In essentialFiles
            Dim filePath As String = Path.Combine(appPath, fileName)
            If File.Exists(filePath) Then
                Dim fileInfo As New FileInfo(filePath)
                report.AppendLine($"✓ {fileName} - {fileInfo.Length / 1024:F2} KB - {fileInfo.LastWriteTime:yyyy-MM-dd}")
            Else
                report.AppendLine($"✗ {fileName} - مفقود")
            End If
        Next
    End Sub
    
    Private Sub GetProjectInfo(appPath As String, report As StringBuilder)
        report.AppendLine(vbCrLf & "معلومات المشروع:")
        
        Try
            ' عدد الملفات
            Dim vbFiles As String() = Directory.GetFiles(appPath, "*.vb", SearchOption.TopDirectoryOnly)
            Dim designerFiles As String() = Directory.GetFiles(appPath, "*.Designer.vb", SearchOption.TopDirectoryOnly)
            Dim formFiles As String() = Directory.GetFiles(appPath, "*.resx", SearchOption.TopDirectoryOnly)
            Dim reportFiles As String() = Directory.GetFiles(appPath, "*.rpt", SearchOption.TopDirectoryOnly)
            
            report.AppendLine($"ملفات VB.NET: {vbFiles.Length}")
            report.AppendLine($"ملفات التصميم: {designerFiles.Length}")
            report.AppendLine($"النماذج: {formFiles.Length}")
            report.AppendLine($"التقارير: {reportFiles.Length}")
            
            ' حساب إجمالي أسطر الكود
            Dim totalLines As Integer = 0
            For Each vbFile As String In vbFiles
                If Not vbFile.Contains(".Designer.vb") Then
                    totalLines += File.ReadAllLines(vbFile).Length
                End If
            Next
            
            report.AppendLine($"إجمالي أسطر الكود: {totalLines:N0}")
            
            ' حجم المشروع
            Dim projectSize As Long = GetDirectorySize(appPath)
            report.AppendLine($"حجم المشروع: {projectSize / 1024 / 1024:F2} MB")
            
        Catch ex As Exception
            report.AppendLine($"خطأ في جمع معلومات المشروع: {ex.Message}")
        End Try
    End Sub
    
    Private Function GetDirectorySize(path As String) As Long
        Try
            Dim size As Long = 0
            Dim files As String() = Directory.GetFiles(path, "*", SearchOption.AllDirectories)
            For Each file As String In files
                size += New FileInfo(file).Length
            Next
            Return size
        Catch
            Return 0
        End Try
    End Function
    
    Private Sub GenerateOverallAssessment(report As StringBuilder)
        report.AppendLine(vbCrLf & "نقاط القوة:")
        report.AppendLine("• نظام شامل لإدارة المبيعات والمخزون")
        report.AppendLine("• واجهة مستخدم غنية بالميزات")
        report.AppendLine("• دعم التقارير والطباعة")
        report.AppendLine("• نظام إدارة المستخدمين")
        report.AppendLine("• دعم قاعدة بيانات SQL Server")
        
        report.AppendLine(vbCrLf & "نقاط الضعف المحتملة:")
        report.AppendLine("• عدم استخدام أحدث تقنيات .NET")
        report.AppendLine("• احتمالية وجود مشاكل أمنية")
        report.AppendLine("• عدم تطبيق أفضل الممارسات في بعض الأجزاء")
        report.AppendLine("• الحاجة لتحسين الأداء")
        report.AppendLine("• عدم وجود نظام تسجيل شامل")
        
        report.AppendLine(vbCrLf & "التقييم العام:")
        report.AppendLine("النظام يعمل بشكل جيد ولكن يحتاج إلى تحسينات في الأمان والأداء")
        report.AppendLine("الدرجة: 7/10")
    End Sub
    
    Private Sub GenerateActionPlan(report As StringBuilder)
        report.AppendLine(vbCrLf & "المرحلة الأولى - الأولويات العالية (1-2 أسبوع):")
        report.AppendLine("1. تشفير ملف إعدادات قاعدة البيانات")
        report.AppendLine("2. إضافة معالجة شاملة للأخطاء")
        report.AppendLine("3. تطبيق Using statements لإدارة الموارد")
        report.AppendLine("4. فحص وإصلاح مشاكل SQL Injection")
        report.AppendLine("5. إنشاء نسخة احتياطية من قاعدة البيانات")
        
        report.AppendLine(vbCrLf & "المرحلة الثانية - التحسينات المتوسطة (2-4 أسابيع):")
        report.AppendLine("1. تحسين استعلامات قاعدة البيانات")
        report.AppendLine("2. إضافة فهارس للجداول الكبيرة")
        report.AppendLine("3. تطبيق نظام تسجيل شامل")
        report.AppendLine("4. تحسين واجهة المستخدم")
        report.AppendLine("5. إضافة نظام إشعارات أفضل")
        
        report.AppendLine(vbCrLf & "المرحلة الثالثة - التطوير طويل المدى (1-3 أشهر):")
        report.AppendLine("1. ترقية إلى .NET Framework أحدث")
        report.AppendLine("2. تطبيق معمارية أفضل (MVC/MVP)")
        report.AppendLine("3. إضافة اختبارات وحدة")
        report.AppendLine("4. تطوير واجهة ويب")
        report.AppendLine("5. إضافة ميزات متقدمة (تحليلات، ذكاء اصطناعي)")
        
        report.AppendLine(vbCrLf & "أدوات مساعدة مقترحة:")
        report.AppendLine("• Visual Studio Code Analysis")
        report.AppendLine("• SQL Server Management Studio")
        report.AppendLine("• Application Performance Monitoring")
        report.AppendLine("• Code Review Tools")
        report.AppendLine("• Automated Testing Frameworks")
        
        report.AppendLine(vbCrLf & "الموارد المطلوبة:")
        report.AppendLine("• مطور .NET خبير (1-2 شخص)")
        report.AppendLine("• مدير قاعدة بيانات (حسب الحاجة)")
        report.AppendLine("• مختبر جودة (1 شخص)")
        report.AppendLine("• خادم اختبار منفصل")
        report.AppendLine("• أدوات تطوير محدثة")
        
        report.AppendLine(vbCrLf & "مؤشرات النجاح:")
        report.AppendLine("• تحسن وقت الاستجابة بنسبة 30%")
        report.AppendLine("• تقليل الأخطاء بنسبة 50%")
        report.AppendLine("• زيادة رضا المستخدمين")
        report.AppendLine("• تحسين الأمان والاستقرار")
        report.AppendLine("• سهولة الصيانة والتطوير")
    End Sub
End Class
