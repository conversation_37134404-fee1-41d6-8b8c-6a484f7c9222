﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Activator
</name>
</assembly>
<members>
<member name="P:Activator.My.Resources.Resources.ResourceManager">
	<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member><member name="P:Activator.My.Resources.Resources.Culture">
	<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member><member name="T:Activator.My.Resources.Resources">
	<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member><member name="M:Activator.Encryption.Boring(System.String)">
	<summary>
 moving all characters in string insert then into new index
 </summary>
	<param name="st">string to moving characters</param>
	<returns>moved characters string</returns>
</member>
</members>
</doc>