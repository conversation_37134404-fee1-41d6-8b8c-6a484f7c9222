Imports System.Data.SqlClient
Imports System.IO
Imports System.Diagnostics
Imports System.Management
Imports System.Text
Imports System.Threading

Public Class PerformanceAnalyzer
    Private cs As String = ModCS.ReadCS()
    Private results As New StringBuilder()
    
    Public Function AnalyzePerformance() As String
        results.Clear()
        results.AppendLine("=== تقرير فحص أداء نظام المبيعات والمخزون ===")
        results.AppendLine($"تاريخ الفحص: {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
        results.AppendLine(New String("=", 60))
        
        ' فحص النظام
        AnalyzeSystemResources()
        
        ' فحص قاعدة البيانات
        AnalyzeDatabasePerformance()
        
        ' فحص الاستعلامات البطيئة
        AnalyzeSlowQueries()
        
        ' فحص الذاكرة
        AnalyzeMemoryUsage()
        
        ' فحص الملفات والمجلدات
        AnalyzeFileSystem()
        
        ' فحص الأمان
        AnalyzeSecurity()
        
        ' التوصيات
        GenerateRecommendations()
        
        Return results.ToString()
    End Function
    
    Private Sub AnalyzeSystemResources()
        results.AppendLine(vbCrLf & "1. فحص موارد النظام:")
        results.AppendLine(New String("-", 30))
        
        Try
            ' فحص المعالج
            Dim cpuCounter As New PerformanceCounter("Processor", "% Processor Time", "_Total")
            cpuCounter.NextValue()
            Thread.Sleep(1000)
            Dim cpuUsage As Single = cpuCounter.NextValue()
            results.AppendLine($"استخدام المعالج: {cpuUsage:F2}%")
            
            ' فحص الذاكرة
            Dim ramCounter As New PerformanceCounter("Memory", "Available MBytes")
            Dim availableRAM As Single = ramCounter.NextValue()
            results.AppendLine($"الذاكرة المتاحة: {availableRAM:F0} MB")
            
            ' فحص القرص الصلب
            Dim diskCounter As New PerformanceCounter("PhysicalDisk", "% Disk Time", "_Total")
            diskCounter.NextValue()
            Thread.Sleep(1000)
            Dim diskUsage As Single = diskCounter.NextValue()
            results.AppendLine($"استخدام القرص الصلب: {diskUsage:F2}%")
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص موارد النظام: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeDatabasePerformance()
        results.AppendLine(vbCrLf & "2. فحص أداء قاعدة البيانات:")
        results.AppendLine(New String("-", 30))
        
        Try
            Using con As New SqlConnection(cs)
                con.Open()
                
                ' فحص حجم قاعدة البيانات
                Dim sizeQuery As String = "SELECT 
                    DB_NAME() as DatabaseName,
                    SUM(size * 8.0 / 1024) as SizeMB
                    FROM sys.database_files"
                
                Using cmd As New SqlCommand(sizeQuery, con)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        If reader.Read() Then
                            results.AppendLine($"اسم قاعدة البيانات: {reader("DatabaseName")}")
                            results.AppendLine($"حجم قاعدة البيانات: {reader("SizeMB"):F2} MB")
                        End If
                    End Using
                End Using
                
                ' فحص عدد الجداول
                Dim tableCountQuery As String = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
                Using cmd As New SqlCommand(tableCountQuery, con)
                    Dim tableCount As Integer = Convert.ToInt32(cmd.ExecuteScalar())
                    results.AppendLine($"عدد الجداول: {tableCount}")
                End Using
                
                ' فحص أكبر الجداول
                AnalyzeLargestTables(con)
                
                ' فحص الفهارس المفقودة
                AnalyzeMissingIndexes(con)
                
            End Using
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص قاعدة البيانات: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeLargestTables(con As SqlConnection)
        results.AppendLine(vbCrLf & "أكبر الجداول:")
        
        Dim largestTablesQuery As String = "
            SELECT TOP 10
                t.NAME AS TableName,
                s.Name AS SchemaName,
                p.rows AS RowCounts,
                SUM(a.total_pages) * 8 AS TotalSpaceKB,
                SUM(a.used_pages) * 8 AS UsedSpaceKB
            FROM sys.tables t
            INNER JOIN sys.indexes i ON t.OBJECT_ID = i.object_id
            INNER JOIN sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id
            INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
            LEFT OUTER JOIN sys.schemas s ON t.schema_id = s.schema_id
            WHERE t.NAME NOT LIKE 'dt%' AND t.is_ms_shipped = 0 AND i.OBJECT_ID > 255
            GROUP BY t.Name, s.Name, p.Rows
            ORDER BY TotalSpaceKB DESC"
        
        Try
            Using cmd As New SqlCommand(largestTablesQuery, con)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    While reader.Read()
                        results.AppendLine($"  - {reader("TableName")}: {reader("RowCounts")} صف، {Convert.ToInt32(reader("TotalSpaceKB")) / 1024:F2} MB")
                    End While
                End Using
            End Using
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل الجداول: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeMissingIndexes(con As SqlConnection)
        results.AppendLine(vbCrLf & "الفهارس المفقودة المقترحة:")
        
        Dim missingIndexQuery As String = "
            SELECT TOP 5
                mid.statement AS TableName,
                mid.equality_columns,
                mid.inequality_columns,
                mid.included_columns,
                migs.avg_total_user_cost * (migs.avg_user_impact / 100.0) * (migs.user_seeks + migs.user_scans) AS improvement_measure
            FROM sys.dm_db_missing_index_details mid
            INNER JOIN sys.dm_db_missing_index_groups mig ON mid.index_handle = mig.index_handle
            INNER JOIN sys.dm_db_missing_index_group_stats migs ON mig.index_group_handle = migs.group_handle
            ORDER BY improvement_measure DESC"
        
        Try
            Using cmd As New SqlCommand(missingIndexQuery, con)
                Using reader As SqlDataReader = cmd.ExecuteReader()
                    Dim indexCount As Integer = 0
                    While reader.Read()
                        indexCount += 1
                        results.AppendLine($"  {indexCount}. جدول: {reader("TableName")}")
                        If Not IsDBNull(reader("equality_columns")) Then
                            results.AppendLine($"     أعمدة المساواة: {reader("equality_columns")}")
                        End If
                        results.AppendLine($"     تحسين متوقع: {Convert.ToDouble(reader("improvement_measure")):F2}")
                    End While
                    
                    If indexCount = 0 Then
                        results.AppendLine("  لا توجد فهارس مفقودة مقترحة حالياً")
                    End If
                End Using
            End Using
        Catch ex As Exception
            results.AppendLine($"خطأ في تحليل الفهارس المفقودة: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeSlowQueries()
        results.AppendLine(vbCrLf & "3. فحص الاستعلامات البطيئة:")
        results.AppendLine(New String("-", 30))
        
        Try
            Using con As New SqlConnection(cs)
                con.Open()
                
                Dim slowQueriesQuery As String = "
                    SELECT TOP 5
                        qs.execution_count,
                        qs.total_elapsed_time / 1000 AS total_elapsed_time_ms,
                        qs.total_elapsed_time / qs.execution_count / 1000 AS avg_elapsed_time_ms,
                        SUBSTRING(qt.text, (qs.statement_start_offset/2)+1,
                            ((CASE qs.statement_end_offset
                                WHEN -1 THEN DATALENGTH(qt.text)
                                ELSE qs.statement_end_offset
                            END - qs.statement_start_offset)/2)+1) AS statement_text
                    FROM sys.dm_exec_query_stats qs
                    CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) qt
                    ORDER BY qs.total_elapsed_time DESC"
                
                Using cmd As New SqlCommand(slowQueriesQuery, con)
                    Using reader As SqlDataReader = cmd.ExecuteReader()
                        Dim queryCount As Integer = 0
                        While reader.Read()
                            queryCount += 1
                            results.AppendLine($"  {queryCount}. عدد التنفيذ: {reader("execution_count")}")
                            results.AppendLine($"     متوسط الوقت: {Convert.ToDouble(reader("avg_elapsed_time_ms")):F2} مللي ثانية")
                            Dim queryText As String = reader("statement_text").ToString()
                            If queryText.Length > 100 Then
                                queryText = queryText.Substring(0, 100) & "..."
                            End If
                            results.AppendLine($"     الاستعلام: {queryText}")
                            results.AppendLine()
                        End While
                        
                        If queryCount = 0 Then
                            results.AppendLine("  لا توجد استعلامات بطيئة محفوظة في الذاكرة")
                        End If
                    End Using
                End Using
            End Using
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص الاستعلامات البطيئة: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeMemoryUsage()
        results.AppendLine(vbCrLf & "4. فحص استخدام الذاكرة:")
        results.AppendLine(New String("-", 30))
        
        Try
            Dim currentProcess As Process = Process.GetCurrentProcess()
            results.AppendLine($"استخدام الذاكرة الحالي: {currentProcess.WorkingSet64 / 1024 / 1024:F2} MB")
            results.AppendLine($"الذاكرة الخاصة: {currentProcess.PrivateMemorySize64 / 1024 / 1024:F2} MB")
            results.AppendLine($"الذاكرة الافتراضية: {currentProcess.VirtualMemorySize64 / 1024 / 1024:F2} MB")
            
            ' فحص GC
            results.AppendLine($"مجموعات القمامة - الجيل 0: {GC.CollectionCount(0)}")
            results.AppendLine($"مجموعات القمامة - الجيل 1: {GC.CollectionCount(1)}")
            results.AppendLine($"مجموعات القمامة - الجيل 2: {GC.CollectionCount(2)}")
            results.AppendLine($"إجمالي الذاكرة المُدارة: {GC.GetTotalMemory(False) / 1024 / 1024:F2} MB")
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص الذاكرة: {ex.Message}")
        End Try
    End Sub
    
    Private Sub AnalyzeFileSystem()
        results.AppendLine(vbCrLf & "5. فحص نظام الملفات:")
        results.AppendLine(New String("-", 30))
        
        Try
            Dim appPath As String = Application.StartupPath
            results.AppendLine($"مسار التطبيق: {appPath}")
            
            ' فحص حجم المجلد
            Dim totalSize As Long = GetDirectorySize(appPath)
            results.AppendLine($"حجم مجلد التطبيق: {totalSize / 1024 / 1024:F2} MB")
            
            ' فحص الملفات الكبيرة
            results.AppendLine(vbCrLf & "أكبر الملفات:")
            Dim files As FileInfo() = New DirectoryInfo(appPath).GetFiles("*", SearchOption.AllDirectories)
            Dim largeFiles = files.OrderByDescending(Function(f) f.Length).Take(10)
            
            For Each file In largeFiles
                results.AppendLine($"  - {file.Name}: {file.Length / 1024:F2} KB")
            Next
            
            ' فحص ملفات السجل
            Dim logFiles = files.Where(Function(f) f.Extension.ToLower() = ".log" OrElse f.Extension.ToLower() = ".txt")
            If logFiles.Any() Then
                results.AppendLine(vbCrLf & "ملفات السجل:")
                For Each logFile In logFiles.Take(5)
                    results.AppendLine($"  - {logFile.Name}: {logFile.Length / 1024:F2} KB")
                Next
            End If
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص نظام الملفات: {ex.Message}")
        End Try
    End Sub
    
    Private Function GetDirectorySize(path As String) As Long
        Try
            Dim size As Long = 0
            Dim files As String() = Directory.GetFiles(path, "*", SearchOption.AllDirectories)
            For Each file As String In files
                size += New FileInfo(file).Length
            Next
            Return size
        Catch
            Return 0
        End Try
    End Function
    
    Private Sub AnalyzeSecurity()
        results.AppendLine(vbCrLf & "6. فحص الأمان:")
        results.AppendLine(New String("-", 30))
        
        Try
            ' فحص ملف الاتصال
            Dim sqlSettingsPath As String = Path.Combine(Application.StartupPath, "SQLSettings.dat")
            If File.Exists(sqlSettingsPath) Then
                results.AppendLine("✓ ملف إعدادات قاعدة البيانات موجود")
                
                ' فحص محتوى سلسلة الاتصال
                Dim connectionString As String = File.ReadAllText(sqlSettingsPath)
                If connectionString.ToLower().Contains("password") AndAlso Not connectionString.ToLower().Contains("integrated security") Then
                    results.AppendLine("⚠ تحذير: كلمة المرور مخزنة في ملف نصي")
                End If
            Else
                results.AppendLine("✗ ملف إعدادات قاعدة البيانات غير موجود")
            End If
            
            ' فحص الصلاحيات
            Try
                Using con As New SqlConnection(cs)
                    con.Open()
                    Dim userQuery As String = "SELECT SYSTEM_USER, USER_NAME()"
                    Using cmd As New SqlCommand(userQuery, con)
                        Using reader As SqlDataReader = cmd.ExecuteReader()
                            If reader.Read() Then
                                results.AppendLine($"مستخدم قاعدة البيانات: {reader(0)}")
                                results.AppendLine($"اسم المستخدم: {reader(1)}")
                            End If
                        End Using
                    End Using
                End Using
            Catch ex As Exception
                results.AppendLine($"خطأ في فحص صلاحيات قاعدة البيانات: {ex.Message}")
            End Try
            
        Catch ex As Exception
            results.AppendLine($"خطأ في فحص الأمان: {ex.Message}")
        End Try
    End Sub
    
    Private Sub GenerateRecommendations()
        results.AppendLine(vbCrLf & "7. التوصيات والاقتراحات:")
        results.AppendLine(New String("-", 30))
        
        results.AppendLine("أ) تحسينات الأداء:")
        results.AppendLine("   • إضافة فهارس للجداول الكبيرة")
        results.AppendLine("   • تحسين الاستعلامات البطيئة")
        results.AppendLine("   • استخدام Connection Pooling")
        results.AppendLine("   • تنفيذ آلية التخزين المؤقت (Caching)")
        
        results.AppendLine(vbCrLf & "ب) تحسينات الأمان:")
        results.AppendLine("   • تشفير ملف إعدادات قاعدة البيانات")
        results.AppendLine("   • استخدام Windows Authentication")
        results.AppendLine("   • تطبيق مبدأ أقل الصلاحيات")
        results.AppendLine("   • إضافة تسجيل العمليات الحساسة")
        
        results.AppendLine(vbCrLf & "ج) تحسينات الصيانة:")
        results.AppendLine("   • إعداد نسخ احتياطية تلقائية")
        results.AppendLine("   • تنظيف ملفات السجل القديمة")
        results.AppendLine("   • مراقبة استخدام الموارد")
        results.AppendLine("   • تحديث إصدارات المكتبات")
        
        results.AppendLine(vbCrLf & "د) تحسينات الكود:")
        results.AppendLine("   • استخدام Using statements لإدارة الموارد")
        results.AppendLine("   • تطبيق معالجة الأخطاء الشاملة")
        results.AppendLine("   • تحسين استعلامات LINQ")
        results.AppendLine("   • إضافة التحقق من صحة البيانات")
    End Sub
End Class
