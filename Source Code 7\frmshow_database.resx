﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="companyname.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="dataname.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="DataGridViewCellStyle3.NullValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk32AgAAAAAAADYAAAAoAAAADgAAABAAAAABABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACEgoTGw8bGw8bGw8bGw8bGw8bGw8bG
        w8bGw8bGw8bGw8bGw8bGw8YAAAAAAISChP///////////////////////////////////////////8bD
        xgAAAAAAhIKE////////////////////////////////////////////xsPGAAAAAACEgoT/////////
        ///////////////////////////////////Gw8YAAAAAAISChP//////////////////////////////
        /////////////8bDxgAAAAAAhIKE////////////AAD/AAD/////////AAD/AAD/////////xsPGAAAA
        AACEgoT///////////////8AAP8AAP8AAP8AAP/////////////Gw8YAAAAAAISChP//////////////
        /////wAA/wAA/////////////////8bDxgAAAAAAhIKE////////////////AAD/AAD/AAD/AAD/////
        ////////xsPGAAAAAACEgoT///////////8AAP8AAP////////8AAP8AAP/////////Gw8YAAAAAAISC
        hP///////////////////////////////////////////8bDxgAAAAAAhIKE////////////////////
        ////////////////////////xsPGAAAAAACEgoT/////////////////////////////////////////
        ///Gw8YAAAAAAISChP///////////////////////////////////////////8bDxgAAAAAAhIKEhIKE
        hIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEAAA=
</value>
  </data>
  <data name="Column1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAACjtJREFUaEPVmAtQVNcd
        xlNtUzUhCr4QM8mY0drEacc0ivJY3g/HJk5MoxlrX0bx0aSN9dGYEGMUiHY6TtNYbY2vqMhjYZeFRRYV
        ooIiROUhoMtjQaM1k6adacJMTcdM//2+u3uRy55dljR1xjvzm+H+7znf9/3POXdZuE9EBoXn+gYYAr4J
        vg1GgCAwCgSD0WBMP1gLARzzEHgADAPfAkMBNbVL5esLZdEfuPTwNB4ORoJx4GEwCUwGU8Hj4Ik+fBdM
        AY+BR0AoYENs5H7Q24TK1xfKoj88JjTj6jF8GGDo74NwEA3iQRJI8ZAMEkAMiAA/AGyQjXBn2AQXhAuj
        9PWFsugPjwnNaMqV56rOAAw4DywEPwVLwDKQBl4EPwOLwHyQCiLBNDARcCF4FLVdUPn6Qln0By42wC3n
        OebqM4QJPD0scmHWA3N/lR20KKMqaPHbdQ8t3/URCVqc9WHQoi3VI+a+nDMs4vltGMsmEsFTgMeOR4nH
        8a40wCPEHRgxZOTY8Qi2cuSKXWXBqw9/NuG1fHl0Y6FMzrDKlEybTPudXYM/s8ZnHIOxn49cvrM8aMEb
        Lw95MITvDheDO8DFuSsNDB310r4fBf8m58rEdLN8J8sm0//gkKf+dEJm7KyUGX8+CU7JjL944M+s4RnH
        cCzncG7w6mznqFW7F0Dz7rwDwa8cnha8Jrc67HWzPL6txB0a4WbuPi0z95yR8H01Er7/nMzaXyuzDtS5
        wc+s8RnHcCzncC41qBW8JucMtf+vDQSvzZ0DeibjSMzYWSEzsbrhe88iZK3MPnheZh+6KBGH6yXiSKNG
        5JEmDf2ezziGYzmHc6lBLWpSmx4qb18oiyogvEFbdZzpmVi9cKykO/gFichucIfNbZaovBaJym+VKPNl
        I6zhGcdoTWEO52qNcFegSW1tN+ClyqBCWexPyLq89Ec3WWT6O+US/t5pmb2/RiIOXUCQBoRqluj8FolG
        SFOhU0yWNtDuxupBv+czjOFYzuFcalCLmtSmB73oqcrSH2WxLxCa9zA+PZ5897jM2lMtETjTUdn1Eo2V
        NGFVTQVOiUG4GGuHxBZ1enD5wP2cYzmHc6lBLWpSmx70oie9VZn6oizqhKzP+x7omba9zB3+4IcShRXj
        6pkKriAIghchuA3BbAhIirv8o4/DHM6lBrW0HeFuwINe9KQ3M6iy6SiLJGR9/lDQMHWbXWbvrZLIg3US
        ndMgJnMLVs+JlWxHoE6JK3a5KekaHJ551KAWNalND3rRk97MwCyqjERZJKN/m//KpC02mYVzSUGujsnc
        CqM291HQAjBM9/+GtjNohEeL7wg86EVPejMDs6gyEmURE4aDj5/ccUIiD5yT6CP1EoPVicUqxWHr40pc
        Eo9VjLd3B8yy0zek/tNb8mxpu/dzbVewIDyK8KAXPenNDMzCTKqsXgUy+lXz0kkZNoncVy3R2eclJv+S
        xFmvSLytA4YuSbB3SUJpd8Bk1f9NvvjyP5AWWXa0WTmGmtSmB73oSW9mYBZm0vP1xatAxrxqrpmOT4Ko
        98+JKbdeYgtbJa6oDSvVOajwSUe7pcD1GSTvXD4bIGwCHvSiJ72ZgVmYCdO9snoVxmwwTwxNL5TI/dVi
        OnJBYs3NEl/klAS8bAmlXZIIo8SjVwdk/vGPpP7vtyBpvNLKWpTjNbRGsEDwoie9mYFZxqcXCLKFQsKQ
        13BDxmwo+PGkzGKt8xisQBxWIt7WjtVxBRx+ZfVf5ZN/3Yac9+W3AaLtBI9Su+bNDMzCTMwGCUNeww0Z
        +1rBjie2O8R0uFZi8xsl3npZEko6IN6lHYmksqt+2db0ae95V13L0YBqXi/woBc96c0MzMJMzAYJQ17D
        DRn7eqFj+o4KicHWxRXgvNraJBGfEElYnaSyaz6Z47gm1u7PIeH/Wl7WqpxvgDsBT3ozA7MwE7NBwpDX
        cEMw6BK/w8fkXMTkFoi0SyK21F8Dz1dcl6Z/fIHpA18BN8BPOx4jZGAWZmI2SBjyGm7IuPTCa7Px0RWL
        sxdvaZXE4nYIdkkytjcZq9yfl87elE9uqc+76lrhaFXqGNCOEj4w4M0MzMJMzAYJQ17DDRmXbnE3kIcG
        cAYTcRZ9NTCvtE1yrMXS09ODqYFdg2pAfw+Qxd2AJYAG3rC0ztpTJXGYlIDJSRBJhlgKRFMg3p8Xiptl
        V3a+3Lx5E9MHvtiASscAFwue9GYGZmEmZoOEIa/hhozfaDnF8xaHbUvA+IEaIM8c7ZCsg4XS1NQECf/X
        oBtABmZhJmaDhCGv4YaM32jdzT++3TvABtqxpWjAgQbKIe6D1PKrsi73mFRWVsrt277fiRXlaEAx3wC8
        6ElvZmAWZmI2SBjyGm7I+Detq2b+8RhenAuSUNgMkTashmvABnSWWmolr9Dq870IuAF40psZmIWZmA0S
        hryGGxL6ZtHkKW/bJSHnvMTjUyux2CnJpS68XN1qMwULS5pl5+E8uX79OiSNVyAN0Iue9GYGZmEmZoOE
        Ia/hRid0U1Hrc7m1Em9ukKSiy5Js78C57JJUrEwqDALhmbJOyTxkkcbGRkjeuVaiAdX4XrjT8KInvZmB
        WZgJ072yehVI6FtF65Pfq5Q55npJtOBXP7YyRTtG3YNqYg7ei7W5x6WioqL3vfDbAMNzpz3Hh97MwCzM
        pOfri1eBTHjLFgJ6VtnRQEGTJNmuYEs7B70LOnwvcgss2pFaam9UjtHQVx9e9KT3SnuDMAszqbJ6FXQm
        bLZtTd17UtIczXd2AedysLugs8DeIhn4qH2u1Kl83rv68NBXP83RIsyALJmqjERZJJj0ILjxYmGdLC7F
        V2CcxyScS346aC80DWH8tQAt7cXl0YEHvei5pKCO4W8wiyojURZ1Jmwp/iH4cp3jkrxQiq8V2FatCW7z
        19WEHh6aWnh40Gtt2SWhN/D7r0ZlsS9hW4rXJ+7+QDJOO+UX5U7to81rJ75KI555hpWH9pJjTs2LnvBe
        o8rUF2WxP2EZxQdS9pyUzKo2WfNBu8y1e5rg74evsht6cO2FdYenJrXpQS96qrL0R1nsT1hGyf3Amgzh
        zaeuSOZZl6ys7JRU/KpPsne6G8F3l+QyNMLvTDp9vtvocIw2Vgvu1qAWNTedxKedFr7ESk9Vlv4oiyrC
        MkuGgncidp6QtKKLknmmQzJquiSt0iXP4suc9qUPf4T0NqOCz/jHEcZyDudSg1rUpDY8ttNLlUGFsuiP
        iZkl88DHc/aflg0nWiXrbKdsre2WzTXX5Jcnu+Unx10y34HPcZ5pBNX+nsDPrPEZx3As53AuNahFTWqr
        PP2hLA4EjIaDdPDPp9+vktVlTZJZ3S5ZNS4t2NZa/HFfd80Aa1pojOFYzuFcani0lP95GwhlMVAmZtlD
        wGZwaervHVjJKvm55bz8GuE2Vzm1oIQ/s8ZnHMOxnOOZq/wNGyjK4mBBCPIIWAPywAXwb8CghD+zxmcc
        w7H6PKVmoCiL9xLK4r2EsngvoSzeO8h9/wUHllyvbNxZVgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        ZAAAAGQBeJH1SwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFdSURBVEhL5dS/
        SsRAEMfxFMeBhWJ5ImIjIsK9gChqJ9gI4iHHFWIhitiKfwr1AQ6tRFAbKx9AsdOzsTisfAOfJH4XMnGy
        Tm5XCy38wQeyM5sMCWGTNE17ImOYCxi27nXMoiAzeMJxDw94x4j5DKsoyCZWrZ4gOzjAMwa+9P2CRmIG
        bGMZ87hDpdDXCx+JGbCFlex6DVeFvl74SMyABi7hHu68opX39WYfiRlQwRJkQBt7eV9v9pHgAB9ZwD8a
        sIGG1StDvjWgD1WrV4bEDSA36GTOrT0WEj2gY12HkD8aQKqYhhzBL6rXVfUpFM4bjZQOOMUZ5Bhuqt66
        ql/gSHo+sojdfK0at6jJugyZgBvShPwEmnvber5f3XgNd3DJpxhXvUlVd2/Tll7I50WSjOIQ8im6qvem
        6vsYkl6IWXTIj/4in1l0fmPAI9wnOcG9tSeGWXTIIGYz/daesDT5ADKnIZLHODm4AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnNew.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        sQAAALEBxi1JjQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAIvSURBVEhLY8AD
        GIFYmY2ZOVxOhH+2ioTgFQ1JoVNAMTawLImAGYg1OViZ4+REBBYriApc1JIRexBorvW8I9bt57aauP93
        J+f/T7HT+i0vI3NATUVl8/l9u/2NDQyWmRsbHlKQlXWHGIMFyIsJLNKVl3gYYaP3oj/R8/eehsT/96YX
        /384swQFP5he+D/dUefPke2b/r+/f/P//3fP/39+fOf/i0tn/xvr6z2BGocJDBQkn6Abhg3DLLh0ZB/Y
        cDh+8fC/pYnxc6hxmMBQadQC+lkAStZ8YEORAbUs4OXnTxGXlnknIimZDjUaAvzNNK9hMxAdI1tw8fDe
        /231NRBcU/lfVlrqM5+g0KHpWw/8949LfisqLTMBaDTIRwwMx9vT1mAzEIZXlUT8z3Q3+39vWgHcAg9P
        z/+T1+9CwTO2Hfx/7O2f/yfe//uf19z9CeibLUDj2RmMlaQeYzMYGcc5GP7fWx8Pt8DN3QNsED7cunTD
        L2V948tExQGpFux48/d/3qKN/4AWXCFoASiIsj3NUYLIwcP7f+nKnSi4asOB/5te/v6/6dXf/5EtE/4p
        6OgfBQcROanoAjCSm+pqIbim6r+MtPQXUWm5syXr9v93Tc75o6BjMA9oOCSSqZVMxaTksySVVH8qaOoW
        gg2GAWpZADQK5GJesKHIgIoWYAdD3wKaVzjkVJnv7t2AY4JVJhSQVukrK28BYXVV1a0EK308AKPZoiMj
        fBwoRkKzhYEBANFegkB9BRnbAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnUpdate.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAA
        sQAAALEBxi1JjQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAS1SURBVEhLlZYL
        TFNXHMarZSNgkfAq2Fb6gj5oy7u8X0JBZDzsxGzADFvRDUQRQhyTMKMBRQkLTgKDzSDOOJkx07mRwIa6
        TYcwtKAzJluCE4cIWhEQReTx7fTaGivUwZf80ntver7v3HP+/3svbR7xCasIasIHhHRCAiGQwCAsXnQ6
        LcmVtfyiO9/+tjpedLdoc/DDypJVE0cq16D+03Ds26acLcz0mo4J4jxRiOx1EoH9YTKM/Xz0/2gla3lT
        Zqpcd6s9B+grMqW3EOjZaMKMNgunqmLhLXEct7am+RpszGsFkzH6rHf7XHMzAUaa9kcjws/lR4ONedks
        e3M60MsFhysTMKTd+toA/ez/OJaCggwZ+CxrJEVyOg025hXs7fKk/6c0VBYGIjGSh1DfFQj3ZyM+gof1
        8W5QR3GgCnBGsMIBYV4O+DDFFWf2eaHzy4CFB7w8Sz2TXRoM/7YBvWfW4s6pcIy0RAEXVCZcPrTAAKWC
        OSfgBVfem2NspKNeicQIziWDjXnRly6Z3ZsXQM14oQGX6pQI93aA0tP5e4ONeUmEDkN1e+OgCnFFdBAH
        m9+VYX9BAFX/TeXhaCyWoTpfgl0aN6yPZpF9cERmohANuyPgr2CeNtiYl1zsdNdYNVOkXK+1atDcmIrj
        1cmo36OijE5WxuD8obcweDbjxd11fbP25QBbguXzw1f0csAcXtMHxgA6nb5GKBQOc7m8foOlqWxtLCf/
        Or9pUQFH9sQiUsmFna31uMLTc/Bydw927CiZkcvlOr5AUCMSSTqINZMKEHBsnq2N4SNFJcTBXSr80JCK
        6z9nYexGAUZ6tlCbP9CWDm2TGkdKI5GXJoVcykdzSyt+vdgO3cNR3Bm8h6tXb6CrqwdVVdUoKyuHWCxu
        pgKMfXC3LYNq/53ZvtiQLEJKNB+qIDYSQ52RHsdG7jouagulaP9CCYWEQxnraWhsnExMStZlZW2aaWv7
        hQoqLS0Hm80uMQmYl3nKtO2AH7YXbqXMdxQXPXViMhuJjcDCwiJGJBKPnjt3AZ2dWshk8kFyfQnNnWv7
        dJo8YxYScKLUF6EBcnRfu477wyOQinkPKBODrKys1Hl5BbP6uyguLplxdeUO0BjWb0yS5zy+LovC7ZY0
        zHabD6jYIkfu5mwMkDXXI5MK+gzeRgVqNBun9QEnT54Gh7NygirT+915qC2LxfoEEfxkTApV8EqyByyo
        lE6I9HFEiKcDVge7IJZM5vODB6glilVFj5CliTaYW7q5uWuPHj1O7QNZokfkmo/ZPpi++TGGtbl43PG+
        ybKN/Z6JsCAFFfB37z+IWx1/j8fj3ySGYxUVn1HLU1f3FRQKz2NUrIeb46Ia7VF7JgL8pBjSDb+oJGOZ
        6unu/hM1NfWQenh8SwVwnBmtu/LDxkeu5y8oYOqKBsE+rhDwVswIhMKB3aVlj2/9248TJ76DWr1uisfj
        PWCzORMsFiuZCiCi29lZ5/A4tj0+Mue+d5Kkgzu3hY7WlsU9ff7SD0P1JyHUS5/0xoSH0H6M7NFZMk7/
        9WHJYDCqPsrOmZJIJOPk3ENvSGRh+J2jpQQJQb9xbxM0hCyC/hPGn2BPeFXLCPr/RFBnJqLR/gNNOg/y
        CHResAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="DataGridViewCellStyle6.NullValue" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        Qk32AgAAAAAAADYAAAAoAAAADgAAABAAAAABABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACEgoTGw8bGw8bGw8bGw8bGw8bGw8bG
        w8bGw8bGw8bGw8bGw8bGw8YAAAAAAISChP///////////////////////////////////////////8bD
        xgAAAAAAhIKE////////////////////////////////////////////xsPGAAAAAACEgoT/////////
        ///////////////////////////////////Gw8YAAAAAAISChP//////////////////////////////
        /////////////8bDxgAAAAAAhIKE////////////AAD/AAD/////////AAD/AAD/////////xsPGAAAA
        AACEgoT///////////////8AAP8AAP8AAP8AAP/////////////Gw8YAAAAAAISChP//////////////
        /////wAA/wAA/////////////////8bDxgAAAAAAhIKE////////////////AAD/AAD/AAD/AAD/////
        ////////xsPGAAAAAACEgoT///////////8AAP8AAP////////8AAP8AAP/////////Gw8YAAAAAAISC
        hP///////////////////////////////////////////8bDxgAAAAAAhIKE////////////////////
        ////////////////////////xsPGAAAAAACEgoT/////////////////////////////////////////
        ///Gw8YAAAAAAISChP///////////////////////////////////////////8bDxgAAAAAAhIKEhIKE
        hIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEhIKEAAA=
</value>
  </data>
  <data name="DataGridViewImageColumn1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAACjtJREFUaEPVmAtQVNcd
        xlNtUzUhCr4QM8mY0drEacc0ivJY3g/HJk5MoxlrX0bx0aSN9dGYEGMUiHY6TtNYbY2vqMhjYZeFRRYV
        ooIiROUhoMtjQaM1k6adacJMTcdM//2+u3uRy55dljR1xjvzm+H+7znf9/3POXdZuE9EBoXn+gYYAr4J
        vg1GgCAwCgSD0WBMP1gLARzzEHgADAPfAkMBNbVL5esLZdEfuPTwNB4ORoJx4GEwCUwGU8Hj4Ik+fBdM
        AY+BR0AoYENs5H7Q24TK1xfKoj88JjTj6jF8GGDo74NwEA3iQRJI8ZAMEkAMiAA/AGyQjXBn2AQXhAuj
        9PWFsugPjwnNaMqV56rOAAw4DywEPwVLwDKQBl4EPwOLwHyQCiLBNDARcCF4FLVdUPn6Qln0By42wC3n
        OebqM4QJPD0scmHWA3N/lR20KKMqaPHbdQ8t3/URCVqc9WHQoi3VI+a+nDMs4vltGMsmEsFTgMeOR4nH
        8a40wCPEHRgxZOTY8Qi2cuSKXWXBqw9/NuG1fHl0Y6FMzrDKlEybTPudXYM/s8ZnHIOxn49cvrM8aMEb
        Lw95MITvDheDO8DFuSsNDB310r4fBf8m58rEdLN8J8sm0//gkKf+dEJm7KyUGX8+CU7JjL944M+s4RnH
        cCzncG7w6mznqFW7F0Dz7rwDwa8cnha8Jrc67HWzPL6txB0a4WbuPi0z95yR8H01Er7/nMzaXyuzDtS5
        wc+s8RnHcCzncC41qBW8JucMtf+vDQSvzZ0DeibjSMzYWSEzsbrhe88iZK3MPnheZh+6KBGH6yXiSKNG
        5JEmDf2ezziGYzmHc6lBLWpSmx4qb18oiyogvEFbdZzpmVi9cKykO/gFichucIfNbZaovBaJym+VKPNl
        I6zhGcdoTWEO52qNcFegSW1tN+ClyqBCWexPyLq89Ec3WWT6O+US/t5pmb2/RiIOXUCQBoRqluj8FolG
        SFOhU0yWNtDuxupBv+czjOFYzuFcalCLmtSmB73oqcrSH2WxLxCa9zA+PZ5897jM2lMtETjTUdn1Eo2V
        NGFVTQVOiUG4GGuHxBZ1enD5wP2cYzmHc6lBLWpSmx70oie9VZn6oizqhKzP+x7omba9zB3+4IcShRXj
        6pkKriAIghchuA3BbAhIirv8o4/DHM6lBrW0HeFuwINe9KQ3M6iy6SiLJGR9/lDQMHWbXWbvrZLIg3US
        ndMgJnMLVs+JlWxHoE6JK3a5KekaHJ551KAWNalND3rRk97MwCyqjERZJKN/m//KpC02mYVzSUGujsnc
        CqM291HQAjBM9/+GtjNohEeL7wg86EVPejMDs6gyEmURE4aDj5/ccUIiD5yT6CP1EoPVicUqxWHr40pc
        Eo9VjLd3B8yy0zek/tNb8mxpu/dzbVewIDyK8KAXPenNDMzCTKqsXgUy+lXz0kkZNoncVy3R2eclJv+S
        xFmvSLytA4YuSbB3SUJpd8Bk1f9NvvjyP5AWWXa0WTmGmtSmB73oSW9mYBZm0vP1xatAxrxqrpmOT4Ko
        98+JKbdeYgtbJa6oDSvVOajwSUe7pcD1GSTvXD4bIGwCHvSiJ72ZgVmYCdO9snoVxmwwTwxNL5TI/dVi
        OnJBYs3NEl/klAS8bAmlXZIIo8SjVwdk/vGPpP7vtyBpvNLKWpTjNbRGsEDwoie9mYFZxqcXCLKFQsKQ
        13BDxmwo+PGkzGKt8xisQBxWIt7WjtVxBRx+ZfVf5ZN/3Yac9+W3AaLtBI9Su+bNDMzCTMwGCUNeww0Z
        +1rBjie2O8R0uFZi8xsl3npZEko6IN6lHYmksqt+2db0ae95V13L0YBqXi/woBc96c0MzMJMzAYJQ17D
        DRn7eqFj+o4KicHWxRXgvNraJBGfEElYnaSyaz6Z47gm1u7PIeH/Wl7WqpxvgDsBT3ozA7MwE7NBwpDX
        cEMw6BK/w8fkXMTkFoi0SyK21F8Dz1dcl6Z/fIHpA18BN8BPOx4jZGAWZmI2SBjyGm7IuPTCa7Px0RWL
        sxdvaZXE4nYIdkkytjcZq9yfl87elE9uqc+76lrhaFXqGNCOEj4w4M0MzMJMzAYJQ17DDRmXbnE3kIcG
        cAYTcRZ9NTCvtE1yrMXS09ODqYFdg2pAfw+Qxd2AJYAG3rC0ztpTJXGYlIDJSRBJhlgKRFMg3p8Xiptl
        V3a+3Lx5E9MHvtiASscAFwue9GYGZmEmZoOEIa/hhozfaDnF8xaHbUvA+IEaIM8c7ZCsg4XS1NQECf/X
        oBtABmZhJmaDhCGv4YaM32jdzT++3TvABtqxpWjAgQbKIe6D1PKrsi73mFRWVsrt277fiRXlaEAx3wC8
        6ElvZmAWZmI2SBjyGm7I+Detq2b+8RhenAuSUNgMkTashmvABnSWWmolr9Dq870IuAF40psZmIWZmA0S
        hryGGxL6ZtHkKW/bJSHnvMTjUyux2CnJpS68XN1qMwULS5pl5+E8uX79OiSNVyAN0Iue9GYGZmEmZoOE
        Ia/hRid0U1Hrc7m1Em9ukKSiy5Js78C57JJUrEwqDALhmbJOyTxkkcbGRkjeuVaiAdX4XrjT8KInvZmB
        WZgJ072yehVI6FtF65Pfq5Q55npJtOBXP7YyRTtG3YNqYg7ei7W5x6WioqL3vfDbAMNzpz3Hh97MwCzM
        pOfri1eBTHjLFgJ6VtnRQEGTJNmuYEs7B70LOnwvcgss2pFaam9UjtHQVx9e9KT3SnuDMAszqbJ6FXQm
        bLZtTd17UtIczXd2AedysLugs8DeIhn4qH2u1Kl83rv68NBXP83RIsyALJmqjERZJJj0ILjxYmGdLC7F
        V2CcxyScS346aC80DWH8tQAt7cXl0YEHvei5pKCO4W8wiyojURZ1Jmwp/iH4cp3jkrxQiq8V2FatCW7z
        19WEHh6aWnh40Gtt2SWhN/D7r0ZlsS9hW4rXJ+7+QDJOO+UX5U7to81rJ75KI555hpWH9pJjTs2LnvBe
        o8rUF2WxP2EZxQdS9pyUzKo2WfNBu8y1e5rg74evsht6cO2FdYenJrXpQS96qrL0R1nsT1hGyf3Amgzh
        zaeuSOZZl6ys7JRU/KpPsne6G8F3l+QyNMLvTDp9vtvocIw2Vgvu1qAWNTedxKedFr7ESk9Vlv4oiyrC
        MkuGgncidp6QtKKLknmmQzJquiSt0iXP4suc9qUPf4T0NqOCz/jHEcZyDudSg1rUpDY8ttNLlUGFsuiP
        iZkl88DHc/aflg0nWiXrbKdsre2WzTXX5Jcnu+Unx10y34HPcZ5pBNX+nsDPrPEZx3As53AuNahFTWqr
        PP2hLA4EjIaDdPDPp9+vktVlTZJZ3S5ZNS4t2NZa/HFfd80Aa1pojOFYzuFcani0lP95GwhlMVAmZtlD
        wGZwaervHVjJKvm55bz8GuE2Vzm1oIQ/s8ZnHMOxnOOZq/wNGyjK4mBBCPIIWAPywAXwb8CghD+zxmcc
        w7H6PKVmoCiL9xLK4r2EsngvoSzeO8h9/wUHllyvbNxZVgAAAABJRU5ErkJggg==
</value>
  </data>
</root>