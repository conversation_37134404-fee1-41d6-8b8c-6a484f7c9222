﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Button1.BackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAB+tJREFUWEe9
        lQs4FWgax49UyJaJmmSU6TJMi9wlaoVkH5d6Srak5nQnEXKYVG7LJuWSgyyhTMi4VGaaIbYpMxG6SKWQ
        W5JKootIOv77fshKZ2eamt3veX7POee7vP//+13ewwHwP2OgiRAjCFFi5MAn+90/Z/iijyEyMnIQaiLb
        tm0TDw0NdYyIiDjH5/O7iNbw8PCzAQEBdjQ+ms0RGuhDGSoeHBysSoLVJA5nZ2dYWlpCV1cXBgYGsLKy
        Yn2HaJ640ECMD2x92+3n56dA4vcoUxgaGuaoqKgskpeX/1RJSWmOmppaipaWFhgWFha2g4IJCQl9xMfH
        c+Li4jixsbGcmJgYTlRUFIey4NBWcvbu3cuhoBwfX1+O1w4vjjuPx3FydrZ2cHBI2LhpU+X6DRtecdeu
        rQoJCW2jHYCenp43GRIn2Lkzc+xz7NKlSzOYAXV19YK3DNTW1b03rq6usk5OTqH79+8X5Obmoq6uHp2d
        naipqcGJkyfhtdNLsGTJkmja8s9JdMQbHWojN2zYYGZrawsNDY2mDzJAjV2wsMTDh1FXX4+WR49Rd6cJ
        129Vo7yiEjcrb+PChWLQUWChqWkizR/H1gwYEKX7oO3v7892oOF3GSjjz2cBRCjzFWFhYYLq6hrUNtxF
        2Y1byMuMw7f7tyLJwxwJW/QQ62yKaE87OK226p2tpuZJ6/7E1hJiZN6VHZGqqmr+oAF29sJEh3Kl38BI
        hy1bkk+d+gGVNQ0oLPoZ2dEeyI9Yi+u50WitLcDr59VoqczHpROhSN61HOtMlO+ryEstpLXsPkwKCgoq
        d3d3x8yZM10GDbCLJ0x0KNRYBpKbN2+uLb54GYWlV/BdNA+lyR540liE3o6bePnwHB5XJqPlCh/PK/6J
        5sIDyPj7KqzSm/ITrdX08vJKpqcKU1PTKvotO2iA3XhhoowBYVY4xllbWzvQJeotKbuG71P4+CVmHZ4y
        8edXIXich0c34lGc4oIk3kIcdjPCpaT1qDlujyi35b0+u7zuM/E1a9a0y8rKmlI8sUED7LkJEWYlU1xZ
        WVmey+X62zvY32UBrl27hpwzvyArdBNunz3Yl7ngcT5ePziOlvJofEPibnbm2GJtDEdHR9ATBlvn7e0N
        MzOzZjk5OUuKK0n8pxJSiRyaLavZkiYmJmp03oc9PDw7UlNT0djYiMZ7zSi8WIb07BxkeJuivb4AvU8u
        QPDwJHqa0vDs5kEccTPuEzeYo9NXcBjWZjqw0lXsHD16tDnFlmA6fVXzjQHWQbBtllqxYsViuqmnAwMD
        Bafz8tDa2oqa+jv4qbAEmT/k40BMPBxd3JG9Ux+vO2ogaKXs76XjVcMRdFdFojzNHgnbzRHrZokrx4PR
        ScdSlTAfDvpTBBRf661dHhBnFWo8l7vWicfjXWfbVVJSgra2dnrXVcg9W4iUrO/wj5AD2OrmiR0+gfAN
        CkXK10Zoq6Nbfz8LPXeOovv2Qby8EYyuS7vQdTUYL6tT0HUzEY/ztqBonzbsNCc/JR1lYQbGubi4FB85
        cgSVlZV40PII54pKkHgsCwkpGXD9ehe2bveE754QhBxMREhMIpx4O+D71VxUnOajp/kkXtXF4eXNUHSV
        +eLFBVd0nF2PZznL0Xb8r2hImodjLn+GxZcTyknrc2EGJtJluVty6TJ+/FcBDiWnI/JQEtxJeOmyZT2r
        ueu63HZ4IzjyEOy3bccC44X4ctas6mUG086cCrbD06o0dFdGoKs8AJ0lPHQUbMazvJVoy7ZA41FDlIRo
        wnfxtN65ClIHSEtamIFPqG6nBOzZh8B9YXBydYfV4sUvtLS1j0lJSVkoKiqGaWhoXtHW0emZPmPGeWlp
        6XW0RlF0hIg+f71619OaU5R9ODoveuHFeUe0n16FB1mWaDi6AMUh2ghf9QXMFGUKaY0SMVKYgVHTp0/X
        Nlyw4FsjY+PL9JfJFxMT06D+icQYYrykpKSyuLi4Jn2XH+gbu3r+FM/sIDsI2srozAPx7GcnNJ60RU2K
        JYpC9XHMVbUv80WKMkXTpCUW0RqJoeJDDbx5ATLEZOIT4m2n/TVhxIvWW+w7m6+wd5Vy472rx/GqKQed
        xduR6mOK9N2G8F+qiK+0Jr9gZ643VSqC5rLM+57e0Jh9cZmBNwwfHE6Ojx6no9/AGEstWce03UvwuvUy
        nX0QqjPX4JD7fJwJmgeb2ZOaaQ7LmP0VjydGPqnI5GzU/eydmL/LQLaXLsXqz97PWvF2beE3VHxy0XHe
        GSm7jVEQboI9NkrQ/GzcPibMRIfy0QYyeNrMwBgDJWluoqsJXj8qpbMPQkXq3xDrqo+CYH2sUJNtoTkq
        hOgfboAay34qz3zGjYq8g3T2eWj+kYsE3jxk7tRjW8/EjQiJ4eJ/lAEJFfmxNvyN+r2vHhShtdAfF+Os
        sNNKiYk//Mu08VE0Z6IwccZHGaDGsp9ib6RwsTRzL7rv5qMuayX461ShM2VcGo2xJzqB6LtwwvhNA78G
        NTGFCRLme1aqC142FVD2AbgWbw6ujlznKFGRuTQ+SpjoUJiBd+IO7/hvUJOx0ZFL/z7CCd2NeahLt0H4
        GmXMmSqVRGOfChMczscb0JLNTPbj4saJnSiNNMZabbluyl6fxn4ze8bHGhCTkRw12/gL6WT6W223VZdt
        15Ab68uMscDvyztxh3f8GtRYuZ5EzCJYeWVVTlTY3PdFaOf/D3D+DTE40SaxZw0ZAAAAAElFTkSuQmCC
</value>
  </data>
</root>