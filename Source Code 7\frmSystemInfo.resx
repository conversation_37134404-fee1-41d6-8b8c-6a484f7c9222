﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="MenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="SaveFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAQEAAAAAAGAAoMgAAFgAAACgAAABAAAAAgAAAAAEAGAAAAAAAADAAAAAAAAAAAAAAAAAAAAAA
        AAB8hIplcYB7jJ57jqF+k6F/k6R8kKJnfZCKnK6LnK5+lKaDkaFufpF1iZ53kKF6ipViZ3BRSkpnZ2l6
        fH2ak4uIlJh0eHpgXWg3OIoXGo0TE2YcHSkdHR9JT1VhZ2ZdYWFjbXVWXWZIUV1aY3ddZn1bboBmfJJs
        hJpzjaB4kqN5k6RxiZ1uh5tqg5RgeIhzj6GFn7COp7iHoLF/mat/mq2Iord/nLJ1lK6An7qFo759nbZ4
        nbiCobl4k6ledYZkgJp7hIplcoJ4jJ5vhZdne4x8kaOMn7F+kaWDmaqNnq+NnK6MnK2NnrJ6jJ6EmKh+
        gIKRjYmJiIVbWVh2b2mepKh0fpZJUFhUWWEoLUUpI14eGzkgHx81NTdBUmNdX2GDhIZsc3g0PkcaHyYt
        N0BNW2pgb4NebYFfboNmeYhuhppvi51nf5Jgc4hVaHcpNEVeeYp8laeLorSIobODoLGBnrSNqLyEobpt
        jaCDoreFo7x/oLl2l7B+nrdti6BUZnVGYnR9hoxodYV7kqSCmauNoLJ3j6F/k6SCkaVpdop6ip2XqLmQ
        pLRug5R9j6BziJlZZ3R6eXpjYV9UTkuGgYGer7xdZWgmKSouMjYvOUE/PkA0Li01MjMpKStJWGRzeX2d
        pKR7g4geHiITFBQTFx46SFZRZXhSZXRTaHdha3hlc4Nic4ZbbYFSXnJRX3AtPk9dbX5/mKmEn7CHobKE
        n7F/nLKGoreCobd3k6d8mK57mrB6mrBzkKd3lqxzkKZSZ3AoPTh7g4lhbH12i51rg5R8kKJ6kaJ1jZ9+
        kaOHnK11jqFpfpBGVmIvPlA5Sl88SVsuPVFgdoh1f4Zya2mNkpadrrpNS00yLjA/PT9YWl0lJyszMjQt
        LCsZGhonLjNZZG+JiYp+ipJHS1IREhUZHCZETVdRYG84RVUmM0BZbH9kfpFfdohca31VX3BSXXA9SF1Z
        boB8lqh/mqqFn7CGn7GAnbF+mrB5lKt9mK17ma97m657ma2AoLSBnrR5lqxedII7UVB3f4RteIlzhZd+
        kqN2ip5rfI+BkqSJmKl6jJ5dcINTYnYsOEdCVGhIVWk2QVM5SFouQFVpe4x6gIihrrucrbySnKhVV1uS
        kZGbmpxTWl43PkIYGRkSDxAWFRUnKy55fH5kbnBXYWkyQlI+TV1GUVlSY3M8SVwtOENbbYFlfZJfd4tY
        a35OYHJVZHdOXGtXYnNqfJN6kaSBmqyJobOBnK18mK15lKl8l6yCnrN3lqh+mq98lqx5k6l5k6pthpxa
        cHR2foNTXm+GmauClaZ4jKB3hpmImq1/lqeJm659jqFxgZVOXnJgcoVbaXw3Q1U5R1o6SV88TmRsf5GW
        p7GcrLiMnKptd4G0uLy2ubloc3glKjMiHyAfHB4jJS4gJC01PUk6Sl9AVmpEXHJIYHZGUV1ZbH5FVWgs
        NkFdc4Nuh5xheY5TZ3pIW29DVGZVYXFKWmpugZJuf5FsfI90iZt2jqB2kKV/mq+AnrN/nrJ5lad2kaZ/
        l617lKlviJ5thJhrf450fIFLV2hpfI94jp55j6KFl6l8kqSLnrCBk6SAlKaHnLB8kKWHmax7j6I/T2FE
        U2dFVGhEV2tNYXR+kqGVp7WLmqmRn62GkZlRXWU0PklEUWJDUF9RX3BHXXFHWmtpdocvSmM1U2w0VW88
        Y4BNXWple45VZngsND5Ybn1yi55NYG81RFE1P0osNT9fbXo7SVdMX3Jge4xzjJ93jJ5ufZBrfZFzjKBZ
        c4hhfpNui6F0kaeCnbJ2kaVzj6V8kqVmeYpye4FRXG1wgpV2jZ9rgJJwg5dzi5+CmKuAlaZvgpR5jJ+R
        pLV4kKKHm65/kaNTZXhPXnFBT2M6S19UZniBlaVdcIJxhZhiepFLYndTa35fcodvf46BkZ6Zp7KdqrSD
        kp02UmctUm8xWHgkXH5PaH52kKJpfY02P0g1Qk0+R1EYGh8fIicvMjg4O0RoeINFTFcaIik4R1Znf5F0
        jJp4kqJvf4t2gpJmcYRwg5dxiqByjqVwj6V3kqVxjaJyi59jdo1ud39fa3xhcYRrf5NzhJZvhJdvhZlq
        gZVxiJp7j6J9kqJ+lKaBl6mEl6iBlqhvgZNaan1IV2o/UGU0RFlEVWlecodugZaAj6CToa+hrbihsLmg
        sbifsLierbmZp7KDk55IZoEpTnQoSXceW4FEa4WFnKx8kKFIVF9KVmFSXWgbGSAeHyIdHiIkKzZBUV47
        RFIcJjMzPUxccYJebHhzh5SBmaqDna5/mKl2iZ1vf5Nzh5xzi595kqV2kaaBmqxyiJpvdn5YaHhfcYRo
        eI1ebYBleo1gc4h2jqB4jaBvhJl+lqhvhpl6jaCClaZugZOElqdkd4pdb4FGU2g3RltqeYyqtr+ksb2g
        r7yhr7ygsLmir7qir7mgrrmerreaqbWElKFldYZAWH4mTIAcVHouY4RtgpmKnrFhcoFxfYpCTVw0NDhL
        SEs2NjgxOkMxO0dDUF8mMD5DTVlbbn9ziZ16lKV9l6mCnK2Gn7GBnK+Fnq+Dmat3iJtyhJZxhpl8laZv
        hZhsdHtFU2ZZan1kc4ZdbYBban5meo9geIxnfZFyhpp0iZtqfpBwgZR6j6J1h5lVZXdwhZh0hphbaXw5
        SV44SmCXpLGkscGjsb6hr72ir72hsLqisLmhr7mgrLqcqbWLmqhseotOcYgfX4QfS24aV39ffpWCna2E
        mKqMobFHT1xwaWtqaGo/RE0kKC0mKi9EUmAxO0pIUWFofY54kaR+mKl/mauDna6Enq+JobOJoLKEna+D
        nK56kqV0iZ1wf5JsfZFpcXtKV2w6SVxRXW9RYnVMWWtbbH9keIttg5dWaXtrgpRwhJZ4jZ9qf5JofI5y
        g5Z3iZx2iZyFlKY/UWY/UWh2hpWmsr6ksL6jr72ksLyksLujsbmjr7mgr7efrraQnquDkJtYbYAqTWoo
        RmIYWoBHbotzjqB/mayCmKwxOUY/Pk40M0M0OUkgICIrKy8oMTw5RVNGTl9ecYR3kaR+mKh8lqd0jqF+
        mKmCm62Fna+In7GEna9/mqx6lKd8lKZ3h5tqcntDVGlWZHpKWGtDVWlUZXhba35abYBbbYJgcIVrf5N2
        jaFsgZV7j6KDlqlVZ3x5j6Fyhpl3i55tgphbbH9fcYGms72lsb2lsb6lsLuhsLmerrmerLmgrbijsbqm
        tL2nsrqGjpZpdX1LVWQeVns0aYxRZHh2kaV4j6E4QE9YVF1rZm1PVV8dHR4sLjI9Q0xIVF8+RFNQYHNl
        fY9uh5t8lKZ/mKqCnK2Ena+HoLKGnq+KobOGnrB6k6d4kaR3jqJkbXZBUmhJV21DVmtFVWo6TF5OXXBe
        b4NhdIhne49le49wiZx0iJpleoxxhJZvgpV2iJp5i51yhJdyh5twhJdIWGycrLqhrrulsbuptb+sucKw
        vcSxv8ewwMiwwMewvsWnsrpzfINcaHJocHszW3giYIZKYndzjaB+ladPVWJuaHZaVm5JTFsWFhgvLzQt
        LTQ6Q0tASFRBTl1UZHVhdYhzi519lqeDmqyKobOHoLKMoLKEmKp8lKd2j6R3j6Rvh5xkbHVGV2xFVGk8
        U2k6T2RHWWxCUmZTZXpVZXhZa35hdYlleY5ug5dvg5Zsf5FmeIx1iZ17j6F3i51jeYp8kKNre45SZXqR
        oa20wcizv8iwvMWrtb+hqa2Zn6WNk5eDh414e4mKjI6HiY9qdH9ldIMYWHtMbodofpJ1jJ46QFYwMFA2
        NlJWXG0aGBkoKCksLDFISlI/RVE1QlFGVmNUY3RjfI1zjZ+AmKqKoLKEnK5+l6l2j6Fxip92jqR4kKZv
        hp5haXNMWW9KWW06TmQ6T2U8TWVJWW1AU2dBUGZWaHxcboFPX3JzhpxugpVthZdyh5l6jqCBlKZofJCF
        mq17kKF/kaRhdotFWW2KlqGnp7CamKSbm6GJiYWNj5GYmJidnaGSkaawtLittLlodIGNkJggUG03Yn1Q
        YXJmfY8+R1VzdIOio6yOmaM0OEEhISRUXmdqb3RLUlwwOkg2Q09GVWVdcoRuh5h3kaKAmaqBm6yHn7GM
        orR3j6RxiJ91jaNviKBdZW9JV2w1RVpPYHZKWnA2SV9DVmtOYHVOYHRDV2xJWm5KWWxWZ3tZbX9jeIp0
        h5p1h5pwhZdug5ZuhJeAladYa356j6BWaH11gZOytryusbqoqracn66or7KnrrKmrLeWnrGksLilsr6e
        oqmVm6A6W3UlRFg1QE84RlRSW290fom4vMakrLVaYHEiJSpESFVTTlFUWWM3O0QcICUZISlLXW5ogJJ0
        jZ57laZ/maqJoLKHna96kqZ2jqR0jKJ6lq1cY29VY3hCVGpFV20+UGVRYXdBU2hIWm82R1xLWnBAUGRG
        VGdldIdSZHZwgJNhcodofJBnfZFtg5V0ip2BlaaLnKyEnK2Hm6yIlqaVrL2mwNOjwNegvtmdvdeZutWb
        utOcu9Wevdiivtuvv8+eoqhgdYchP1YnKzIZHCJDRU+Fg4Wmrraxtr10d4I4Oj1tcn0/RFBbY2s+RUwr
        MDkcJC9TY3NsgpN2j6F9mKl+mKmGna+NorWBmqx8lKl4kKZ8l6xaYW5FVWtGV2w/UWdGWW88TmRDVWpC
        VGo6TWBEWnBFV2pJWGtWZnlYan1bboFleIphd4lleYtxh5lziJp2i56HmauOo7NneYlYaHmHorepx9yq
        xt+jwtyfv9mevdidutWdu9advdehvdelv9aorrSBi5QjQl8wNjwjKDE3QEpASlRMVWqMjpWHgIaCfH1s
        bnVUVGNTWGMkLT1OWmccKDdYaXV0i519lKWKobCMoLOTp7iSp7WFm615kKV3kKV6k6ZcY3RHWXEuQFZT
        ZnxFV2xAUmdDVWpDVGlCUWVDWGxEV2s/T2NOXXBFVGdYbH5aa35vhJZziJpjdYd5i55+lKdoeYt2hphb
        a31whZeXqLeduM+syd+qxd+nxOCmw9+lwdykwNukwdynwtyoxdyuvMaMkZhCWm9MVl4vNj1WV1tPS1E/
        Rll6h5mXmZmXmJhhbnmWpK1ocH0gQVw/Z4IzP1JZaHaIna6Sp7eQpLWUpreMoLKPpLSbr8CCl6pzjKB7
        lKdbY3U8TmJGWW89UWg+TmROYHQ5SmBGWWs+UWVSZHhLXXJGWW5HWWxHW21IW21Yan1aan1kdIdwg5Vy
        hJdxhJZoeYtyg5V/kqNyhZWGna2ht8aty9+tyuCtyOGtyeCryN6pxd2ow92pxN+oxdqzyNiKkp1ZXWY+
        SFFARUtJVVyfp6pwdoFDTl1LRUNgTEZCTFdHYXJ6hZMlR2grWH8yV25YZnWYq72etMKZrb6Zq7uSpbeP
        pLWOo7WInq+FnK+Bmq1KUmhEVmtAUmhKXnNMX3NDVWlTZXlKXW88TmBEWW0/UmdOYXZGWW4+UWZIWW5K
        XG9ba35aan1gcINVZXhwgZFleYxrfZB7jqCDlqmInayVpbakv9Gvy+Gwy+Cuy+Cqxt6qxd+pxd6nwt2k
        v9mow9yWnqlvb3YzOTxKTlM8RU9XYWqbo6s/V25UW2hPYXQnTmccUHMxY4IeSnAgXYUdU3NRZnamt8Wp
        vMmjt8WdscGarb6ds8Oar8GRpriKoLKHoLNOWG00SF07TWJHW3A3SFxGWm89UmdDVmpJXG9DVWpFWG5D
        VmtVaH1JXHFOYXZLW29LXW9RZHZJV2pHVWg5RlddbH91h5lyhpd5jp9/kqSClaaessGsyN+vzOGtyd6v
        yt+qx9yoxN2oxN2qxd2rx+Gvu8hhY2w2OjxPVlZWXWM7R1ArSV0lU3odXoccXH8sWHIeXYAxYX0aXIIW
        Y48aUXVKbYSmusirvsupvsypvs2jtseLorKPpriKn7GCmqyDm65PW2kvQFdBVGpBUmlBUmdAUmg8T2VE
        WG4/UmdIWW5IXHBFWGxKXHJMXnNNYXZLX3JRY3VGV2pGV2pTYXJKVGNMV2ZufpF4ipxzh5l8kaOClKaG
        maeku8+qx92tyN+tyN6ryN+pyNyrxdugu9amvtacrcBIT1grMDNKT1NLVF1BSFInUGoaaJMZZpcWZJEe
        VnYXXYMUWHscXIUaYIoaTG4jT22Xq7unuseluselucmgtMObr8CcsMGVq7yGnK54jJ5DUF9GWG5AU2hJ
        XXM7TWI4SWBHWXFCVGxCVmpRZnw9UWZMXnJGVmtJXHFKXHBUZnlIWWtLXXBNXXAqLzU9Ojk4MC5bYWpo
        eop3i5txhZh/k6SAk6WWpbSKoraHnLeInLWGm66AlqyVpbOnqLOypaGHh4hMT1UtLzI+QkdRWF03QUoh
        TmwhYZAaZ5cbYYwaU3cdVHccTnApU3JBYXo1U3McPl9og5eouMait8SgtcOhtcOTqLiKnq+Hkp6UnKeR
        m6ZKWmk+T2Q4SFs7TGIzQlpDVWs/UWg9T2ZEV21BVGlGWW1EV25QYndNYHVKW25GV2lUZHdNXG9IWWwy
        MzctJyRYUEo2MjE8PT5yg5Rrf5J1iZp2ip2CkJ1oeIl+j6BtgJCClKV0iJhXY269sqXMp4tuVURRS0yq
        qatsdIFrbW1LUFcpS2QjWXomV3knUnJTZXtpeYqBj5iMmKCWoKlXbogsSWc1WHKUprODmaeAlaV0h5lo
        eoxRYXBsf5CDl6iKmKVQXm83SmA+UWdGVm5MXHNBU2o4SV9CU2g8TWNEWG1MXHJFWG1IXHBOYXVWaHpD
        VmlGWWtFVmhOX3JUUU9SU1JDTVg7QUk1PEVhdIZofI1tgZNoe45yg5V7jJ91iJp4jJ5tfpB+kqR+goXM
        zca8ppRXQVl6UoywoLLW1NCDhIY4PUIyU3Axa4xDepeAmq2WqL2VrsOgudGlv9emwNqRqcRtgZUqTmlV
        YW1RYnJqgZRaaXxLWm08QU13iZWToKuPnq0/Tl5HWnE/U2hFWG4zRVs5SmBMXHBKWGs/T2RDWG05Sl9J
        W3FCVmhLX3JHWmxSZXpGWW1RY3ZMXXBXWlxASFFGXG89UGFJWGpcbYBrfI92iJt2iJpwgpRcantleIpt
        gpOGl6lZa326wsTo5+SPiotlXWZxVX1zXHPNzs+8vsBOVVxEX3mIkJ2hvNSjw96nw96rxuGtxuGqxuGq
        xuKoxeGlt8wvT25bY21YbHpneYxUYXdSYHVGTVldb4NviJx1jqBaaHk4Sl9DVGk/UGY7SmFJWW9CU2c3
        SFxJWG1FVWoyQ1c+T2VAU2dGWW5DV2lBVGlIW3BFVmlOYHMyPE00QlZNZXhBVGhCVGlDV2lJWWxgcoVq
        e45sfZB0g5RjdYh/kqRwhJWEl6l9i5vT2tne29diZWdYUlJbV1ZTU1NPVVk6QEo4RVtFS1e0ucKDl6+G
        obuVsculvNOjucyuvc2QorearLxue5BdZGssOE4qMlYdHlgrMlVARlI0O0Rcc4RccYOLnK51i51SZHhR
        Y3hTZXpKX3REU2c6S2BGVmo/TmFYaH1GWG43SmBCVGlHWWxJXHBNYHVHWWxVZ3o8SVk9TmFKYnZMX3VL
        YHZFXHBIW25LX3FoeItwgpRwg5VvgZN0h5mAlKd6jqFyg5N/jZyYpK4/SVQ8Q0syOEJGREE6NzM2Oj83
        PkorMTuEi5SJlqehqbSforB/iZe2vMHAwsaytbyrsbmWmaNud340NlkpK2IjLFwrMl1LUGNAP0NnfZB0
        h5lYZ3mAkaR7j6BMYXUxQ1g8TmU/TGNJWG05Sl9LXHBGV2pTZ3xVan87T2NCVGpCVGhQYHVTZHdHW20r
        OUo6S19MZHlEWnBUa4FKYHZKXnJKXXBMXnFabH5qe41neo10iJxugZR5jqBre42Fkp+Zp7E1O0cyNTxC
        RUuUlppKTFRbY21odok2OkhNTVZcbHxhaXZhZHJRXW9ganqzusDHy8+xucGrrrVkcYQ7QFA1OlcwNlM3
        PlhOWGs4PD1VaHpdc4hcbYBgcYWNn7GCl6t1iZ5rg5ZNYXY5SV9VY3lFVmtAUGROYXY8T2RMXnJLXnNJ
        WnA8TGI8TmNDVmoxQ1RDV2tLYnhHXnQ/VmxMYXdMYndJW3BLXnBMYHNneYxwgpN9jJ1zf4hkZmhgYF6N
        lp2VoqtPVmWBlKyduNCiwNmiwNyiwd6kvtlqdYZFP0Q0QVU9SFk6Q1B3h5eFmKiPobCTpbSQo7Saqrdv
        e489PUQoJ0siI0g/RFwuL0I6P0BIVWVSZHY4SFxKWnFQYXVSYndygpSImaqClKZieIpIWm5hcoZWZntJ
        XHNRYXZGVmhKW25LXHJIWnA+U2lFVmwwP1M4TmVLYnhDWW5PY3lGXXJIXnNPYnZLXnBDV2lCWGxJW2xe
        Y2deWFFDPzpUXmlkc4JaZnVhbXxfcIedutWlxN2oxd+lxN2iwtujwNuCjZ80O0s7Qk08Rk5QXmxSZndm
        eIhxhZd6jp+DmKeEkpxFRUYtL1MtMFYxM2EZGi5BREM2PUZZan1sg5ZGWnE4SF5JWXBRYHNcaX5gboJw
        iZprg5dIXXJMXnJdcodAUmdBT2JKW29FWW9JWW5IV2xLXnM+UmhBV21IYHZEW3JGW3FLYXZIXXBOYXVD
        V2pSZXpNYHRLWms+PDo6NC8sJyQ6OTlLWGdIV2pWZXZ3h5hgc4umxN6gwNyhwNyhwNyhwNyfvNdQVGEx
        NTxOVlxDSU44QEk+RlFMV2Jkeox3i55/j5xRTU1TVHd6eadlZJMiITpBQ0M8P0VrfY6No7ZOY3hIV2xR
        YnlDVGlUY3hWaX10h5mHnK9/lahzjJ5MWm89S2Bgb4FOXnM9T2QxQVU7TGBFV2xIW3BGW3FEWnBMYHZE
        WGw/U2dDV2xDVmk/UmZEVmtBUmdFV2ogHBozLCY8NTJSSUBHT1hTZXZPYHJyhpVVW2ltgJimw92mw92o
        xd+lwdukwduQobY0NDtWXGM7QUc9R1IyOUFCRktoeol7jp6Roa5VUlNAQ1WLjbJ5dKNCNz4vLjxOQzyP
        naVSZn5vhZpCVGpDU2lMW288TGE8TmJYaX5eb4JdbIGCkqV6jJ5ugZRWaHtFVGdJWWtOXHBAUWY7TmNA
        VGhFXHFFXHE9UWVBV2tFWW1BVGlEVmlKW25KXXJBVWdFVWUfHBsxKyYzLShWT0VRTUpSYGpVZXVugpN2
        hpVZXGpGUWZET2NKU209RV9DTF5BSWBJRkxUXGRcZ3Bmcn47Qkg3OD11hJGcr72is8JTUmI0M0RSUmNG
        P09ZRUhEQW5gY2+EkZ2Pnq+gscB/kqRJXHI+TWJAU2hPXnNAUWZGVWpAT2ReboJbanx8jJ+Km6pwhJhK
        XXJGVGpNXXFJXHFDWG5NY3pFXHJGX3NLYnlKYnpHXXJDWW5BUmhMX3RPYXVHV2kgHBwqJCAuKCZJQTta
        UEd1amReYWVsfYp2jJ9yf45GTFxia35YXGmoloW3knl9XElIOTZWSkJoVTpmW0tZTD8wMjZ+kZ6kuMao
        vMtucY1qZoItKiohIiBdYWiGhYtpb3pufIh+kaV/laV9kqVmfJE/UGZEVWpGVmxKW3E8SmBHWWw9TmRF
        UmZNXG9pe42Imqx+lKRqe5FXaH1QYHY/UmhGXXNJXnRIXnRMYXdMZHlJX3RPZHlLXXRLYHZAVGlGVWUl
        Hx07MytGPThnXlJrXlJUS0NORT5OSkhjbHRndYBIT1h4jJ2HdWWHXj97ZFlKOi5vUTlqUUNrTixnVD2M
        akJkV0uNn6yovcmpvst8hZFXWmBBQ0Y0Njp5fYRhZ3JfaHRmc4KKnrCnusmjtsSbrL1whZhKXXJGVWlQ
        YHVeboREVmtHV2pCUmZIWW1YaH9fcohugJSKmaqfr75ugJI+VGpIYHZMY3pMY3lFXHJNZXtNZXpHXnNN
        YXdHXHJNZHpBT10rIx4wKSM2LilPRz9kWE1hU0tYU01waWR2bmh+i5Z4jJt7j6KVhXyidldNPDQlHx5i
        TTeDfXxzVjdvVjmLbESFZ0WGfXCntbypvct2foiKjpRxd35xd36Ah49+g4t7hpCOmaJ5kaVsfpN7kKKA
        kaRkeIpMX3RYZ31KXXJAUGVKXG9bbH9MXXFNXHFKWXBEWW9UZ35hcohCV2xDWnE+VWtKYndKYXhQZnxJ
        XnRJYHZGXXNPZnxKYndKYHZGWnFBT10nIRw6MixEPzpDOjVYTUJVSkBnYlxfWlVuZ2CEi5R+lah2ipt1
        hZSOk5ZqY2Q/MSlpRh92aWByXUiIc1iMdFuBaE6KaUKFc1uktMNjbHukp6mUmp+Nk5mcpKqZoamirrai
        pa18kaNmeY2So7SZrbySpbV/laVJWW1DUWdHWW5eb4JMXXFIWW5MXnJSY3ple5Fjd41Yb4VDWG1HX3Za
        colrgpZKYXdKYXdKX3VKX3VFXHJJXHNOZXtSaoBFXXM9S1omIR06MStEQDtHPjdANi9BNjA0LilMR0Jn
        X1ltc3h7jp57kaVugI+FmatzdnZ/enWQlJeGlaaSr8WevdqirbaQdl6OakWLZjmntsB/ipvAxc2/yNDD
        zNTI0NnGzde7w8q5ub6Xq7l6kKJbbH+MmqqLnK6LnK1UaHxIVmpTaX5BUWdJV2tQY3hZbIJecoZ+ladr
        g5Z8kqR+lqmDmq6Lo7SVqruPp7lTan9XboRMYndOYnhPZnxCWW9OZXtKYXdEU2IrJCA0LyoyLSo+NTE3
        Lyg7NzIqJiExLChPSERkXVlnaGp5iJZseIZsd4Rrc31pcntveYVwfYuPq8Wmwd2iv9qWrsGZg2uPcEui
        s7y7zNfI09vL0dq8wcarrbKnqq7Bx8rHy8+PoLCNnK5YboNba4J5jqOEmKt7k6ZUaHpCUWVXZXtXaHxO
        YXZqepB+lKd8k6eCmq9XbYNwiZyKn7CFna5hd4qDmayNpLZkfI9ZcYVKXXROY3lRZnxVan9KXnVJVWQt
        JyMyLCcvKSZEOzg0LSc9ODY2LS0tJydvaWVsZmFTSkRIRkd+jJGMk5aQl5yUnKCjqq6co6ugutCnxN+o
        w96oxN2owNSXi4GZpaqlu8qrv8y7xcvEycu8wMSzu8O0wcusucVjeIx8jqCMna9ug5ZtfI57iZyLna5e
        cIJQX3JQYnhIXHFZboJthplgeI6Al6uHoLOBma1+lqhKX3NddIpXboNccIV/lKaIoLJedYpZcIZEW3Fb
        b4VTZ3xNYXVFUV4sJyIvKSQ2LyxBOTc9NjM4MzE2MCwwKyo7NDFVT0plXVhZV1Rla3Kbo6uyvMK0vMOy
        u8Wdpq6Bj56ktsitvc22wM21vce/xMuuvcelusqkucqnu8qrt8Ckrrehs8OhtcOcsMCTorCKmahqd4d5
        i51Zan50h5pfcIJ4jqFZboFTYnmTpbd0iZxshZmFna9ngJNbdYtlfZNpgJd+mKqVrb5wiJtOZHlWbIFw
        iZ18lahheY1OZXtdc4pQZHlFWm1JWWktKCMuKSUoIiJDOzc5Mi86NTI7NTEvKis6NTJdUklZUEhaU09K
        Sk5gbHqtvcysvMiuvsudqLKJlJyyvsiuucSfsL2VqbaWqbiXqrqbr8CcsMKitservsmUnKmfssOdscKe
        sMFMXG9ecYVrgJVleY5hdopEVWxfdIpofZFkdYhpe5CAlaZ5j6SGnbOInbCZr8B/lKmOp7l3jZ5xh5hd
        c4tthptSaH1OZHxSaX9mfI6DnK5+kqNXbYFYa39QYndOWWgxKyY1LihVTkdoXlQ1MCk9ODQzLSxEPz9G
        QT1RNSdcQTNLOTpGQ1ZPUmWcq7ipucapucecp7GHi4ufsL6BkZ5UYG1PW2pUYnJxhpmEm62TqLmar7+a
        r8F8i5ecsMKbrsCarr+Amappe41ugZNXZnlVZnt9kaSDmKuJn7B9jp1oeY5vhZqKobN7k6dWboZheY9o
        gJVmf5R5kqaFnK+PpbWXrr1ieoxPZXtRaH5UZ3xmfJBzhJONobOJnKyNoK5wcXVeVU5gV1B4b2VtYFU3
        MSs6NDAuKik+OjlVUlFLNjBDMi1TPC9maWx7gZOisr6wwM2wvs2eq7ePl56isr9yeoZWZHBteohCSFBX
        an1yiZuJoLCVqrqar76ds8CitMGbq7qaqLlbcoZrgJNacIZbb4ZxhppwgZOJmqurusmouMmEmKyFnrN8
        kaVphJd/lqx5kqSFnK+Cl6uOprd3jqBheIxvhpt/lqlRaH1HWnFSZ31NY3hOY3hQZHqNl6Cvqaexp5+m
        mZCShn2JfG9bT0c+NzA+ODM5NTI7OjlJSUhSOzdeQTVrSDuNiYejpa6dq7imtcOrucmgrbqQnaemtcFp
        bn1ETFlye4gxNkBbaXxASFNidIOFmquYrb2csMGdssKfssCdr72arbqbrbuZrLyWp7iis8CWqbh+k6Vk
        eY90iqBfdoxwh5uAmKyWq7+JoLSOpbmNpLaOp7d+lqmEnLCes8SHnK93jZ9TanxUaX5QZ31OYndLX3Rz
        hJK0raW4saqxqKGpnZR6cGeQgHJfU0pEPDc6NC9FQTw5NTRIRkdeR0Ghhn2iiHq3tbbO0djIzc+zucC0
        vMWhr7qPnKd1fo9QVW1HSmNGS2U/R19MV2pBSFVVYnV1iJmcr8CdssKessKbr8CarsCGlqmCkqd7j6KM
        obJ4ip1neYxmdYh1hZh9jZ6Dl6t2jaF8kaRxh517lKh/lqlvh5pxiZt0jJ5le5BugpZ6j6KTqLuGm6xc
        cIZbcYdLXnNecoOmoZ6ooJispZyuqJ6nmo9xaWF3aV1nWVBNQj4+ODRJRUFAOTdHRkdyboGKiJ9pZnDB
        x83Fy8yssbOirLWvucObqLONmaRcYHg+QmQ/QmU7PmA0OlwyOFdBRVhJUGNsfIuClaiVqb2htMShtMOa
        r72Cl6uQprqOo7WDmKqSpraInq6Po7OVqbmNnq6QpbeYsMCJorSVq7+AlKeMpLmSqr2Gm6+BlqqgtcOB
        mKmKpbVgeI5lfI9SaX9QZXtPZXiJjZKto5qupp2spZywqqGroZeDenFgVUtaTkRMRD1CPDddWlRUTUlo
        ZWSIk6KbsL2Wq7qNoLSHkZprbHNganN9h5CRmqSDjZdHS2E6Plk3PFk2O1Y2PVgsMlA6Q1U7PU1SWmR5
        ipN3iZCSpK+bsMCcscCOpLaPp7mFnK2UrL2OpLSEmaqLnrCInaxkeYttgZN5j6Fnf5NpgpZ1jaSar8GD
        mq2BmqyFn7JshZ1ngpd6i51xg5Z7kaVbcIRld4iUn6SspZ2yrKO4saiooZixqqGzq6GWjIRYTkRWSkJE
        PDZYU05eVVF8fHmutLKpr7WTqsCYqbuJkZ14fIBTUFBTU1pSU1VPU1diZ25GSVw5Plg3PFk1OlhLTWI8
        PU1cYnRSUGVgZmqwu8JgaGlGVFdvf42KmaeLoLGIn7KNoraCmKp0i5+Hm6x5i5+nsr+crb2WpraFk6aF
        ma11jaB1j6N1iqB4jKKBl6xof5N6kqeFnq9rgpVviJtvhpt6kKOLk5m5rqatp56vp56wqJ+mnpWxqaG1
        raSmmpJaT0ZWSkNAOTZUT0t6eXbAwcXEyM6am6N5gImMk5prbYBxbnaLeFdgV1FFQUFFR0lYWl44OU04
        PVY4PVg1OlVESlZhaHBkaXRsbneYlo6MjZCNk5FJUlRveX6JkJeJkpxieY5ieY1nfpNba4JneYx2iJtk
        c4h0hJh4i59whptpfpFofpRqgpdVa4BddIt2j6J6kaNvh5t7kKNZa4FfdopddYttgJKmo6C6sqqzq6Ko
        nZSwqJ+wqaCro5qsopmxpJxiVk1OQzxPR0BoaWiiq7amq7ecqLeGkqJOVmRZZHNnbYdnZG2djGaGc1NJ
        QDk9P0FYWV42N0c2OU42OE40N09HSFBna3Fxc3hydGeZmY+GkIt1goFkZmlqc355gIqhlZCsr7Kipq2h
        p6+UoauUqLefsLyLlqKQn6+Gm6uQpbKQpraFmq6Wqrigsr2nrbONnKeQpLGDlqN7lKZ6kqWfsLykr7Wb
        n5+kmY+ropmroZmpnJOonZWxqJ+xqKCsopqpnZRuYVlORj9iY2dqdYJpdIRncX9ga3tjbn9XY3NeaHaA
        iYuBjJWVjn+JfWxBPDo8Pj9VWlw4OEY7OlE6OlA6PFFYXFmDiYCQlo15fnZkenBfe3lSY3FobnZ9kaOK
        m7CqoZqpopqvqqKtpZyonpavqKKakYuGeXBmWlFXS0JfVlNia3lgaXedmJWnoJmnn5ekmZGmnpamm5Kg
        mJCin5yooZqlmZCdj4GglYehlYqnnJOmmpGgk4eflImglYukmpCjmIx9cGhnZmdeZ3lSXXFWYndTX29X
        ZHhXZnpbZ3hXYG9jbXqPn65rbnI2MjA7Ozw9PD5XWV47NDVIQ0NQS05MSEpTVVtueYRlbHtMUWVSW2Rc
        Z3tNVnJjZ3BzhJh6kKKnoJehlo6fk4yimI+km5GtpZymm5OShn1oW1JeVVGHk56RorSKnK2AhY2poZig
        l4+imI6imJCflIyhmIytpJyyq6Kqo5monpSiloyfk4ikmpGimo6imY6kmpGpn5ynoqGppKSvsLKJl6OW
        o7KQnayUobCToa+UorCapbOIkaFga3pganuKmqqTnKNWTks2NjU/P0FdX2NCPTpRS0ZsYl1fWFVrcnlt
        f5JkbodBP29LS1xRVmpVXnprbnWJm6yPo7V8cW+dkImckIiaj4afkoukmpKqoZmimJCGe3daYWtlbH1r
        dodrd4lndIZzdHufoamorLGwsbSztLe0tLa8vL+9vr+2t7idnJ+ysbSys7ivs7ikq7Kpsr2otcCntcCe
        q7lvfY1rdYdodIVncYFfbXtpdYRpdYNreIplcIJteopkcX9lc4GHmKaVprJ4en5gZ2xSUE9jaGlxa2SM
        gnyFgHyLk5uVpbKQpraHmKtZV4VCPlVCQl1KUHFgZnRhb35cZnNkX2CfmJWlnpyjnJygm52Wk5OXkZGH
        hYtrcX1XYnRdantveoxwfY1ygZJhbHxibHuGjp2dprWsuMeqtsarucexv82vvMmlsb+LlqaLlaSQnK2T
        obGJmaiJlqaEkaKGl6WFk6SDkZ+irbmpt8KaqbepuMKotsGzv8myvsizv8usuMOgrLmCj5uGkJqAjZh7
        h5Jwamt3e3yWjIWDenR3fIKAjJZ9iJdwfIhyfYZWWmdFQFRJQmRSTXRbXHlZU1BiZWlzeIJ9hpV0gJF7
        hZaIk6B5g5FtdIR5g5KMlqaLl6WGkqCCjJqAi5mAi5p4hJRjb31ibXtha3tweoeCjp5+iZp5hZeBjJyA
        i5t8iJh5iJWeqLWfqraVpLKXp7OWorCapbOSna2Nmqh8hI6fnp2bnqONj5KOjI2Mi4yTkpKQkI+UlJOg
        n6GioJ6hnJaSj4yXlJOPj4uChYi/tq6Ph4NzcXKKiYd5eXh4enmEg4J4e3d+f3iIcE2AXDJ6ZFJ+Y0JT
        UVVjcIJjcIVue42Kl6aVn62Yo7GWoa+Uoq+Qnatzfo5lcoRmcINmb4FpdoiJlaSPmqiAjZt9jJeQk5iV
        m6KRmaGWnKeRmqKNlp6NlJqUl5iFiY56g5F3gI91f41teYdwfot0gpCBjpuTmJqdmJOVjo2HhISQjIuY
        k5KclpKPiomfm5ijn56moqGqo6Ccl5SWkI+alJGGh4mwp6KYkZCIgoKLhoOMh4WPioqJg4ORjImOiIGF
        dGF9bV5wamR+dnFwaWRodIJwfI2Cjp6LlaSSnauYoK+cpbOdqLWUoa+QmKJ5hZRyf5J5iJqAjZ2XorGW
        orCVn62HhISIgX2CeXh1bm5vamtva2dnYmJqaGpuaWdqZGRtdHp3gI56hZF/iZaFjpaCiZB4dnt1c3F3
        dW9raGVoaWtsaGxvbW91cXRycHR1dXZ5dnt1cnNycHJ0cHVxbHB+eH1xcXVkYWJeWWBeXGBhXmJeWmNZ
        VGBTUlNYU1RYWVhfXV5aVlZXWFZRTU5iYFpdY2GCipaJlKGNl6aVobCTnqyPmqmRn656gIVnZ2NtdHRr
        doaCj6GZpbOZpbSYoa1hY2NNS0JYV1BPS0ZOSU5SUVJLS0VJS0lPU1VOS01XVFJRVU9XV1tbWmFbWFpa
        WlhaXlhYWFdUVE5QVEtNUEhbX15WWVpaXFxbXF1cXmFWYVxNUU5ITkdGTEhUYVteZ2JjbWlmamtARD9H
        T0tLVVJOWldIT1BHT1A9QD1HSkhGTEtLTUw9PDdJTUk6OTQ7PThbZlpkbmZyeXZzdnN+f4KEi5WRoK1z
        eX9ZW1NlZl1mbGJcYFqGjZaTnauHkpxbXllKS0FLR0BfX1heX1ZVVmRTWFtJTEVIR0NRVE9QVVJPV09U
        XlRVWldaXWVRVFBRWVJNWU5aYltRV0xLUEZJUUZcYl9ea2lfcGZWYFVaYGlZa2FLT0hHS0JFUENRZFZM
        XE5MWU5haWhDRz1IU0ZLWktJVUpIUklAS0E/ST84QDg/Qz1ETklATEJMVE5CSUI/SUEAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==
</value>
  </data>
</root>