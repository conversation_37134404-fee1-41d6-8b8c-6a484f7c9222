﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="Label5.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABVFJREFUWEfF
        ll1MW2UYgBeNkbE4M7Mb467UGzON0RANovgTDQ534b2a6KWZF0tMHNEwzbIp25gwHBsLdKCDAd0GbuWU
        ggNKS0uhnP6cntNzetrT0x7Kz9yc8cKQNNtev/frOP3hCD1Xu3gC/X6f7/3e72231R3c/1AxbCwXQYh+
        qmmZ5vb29seN+svBsHErrNbLu1RV689kVuHmzb9gdfUWFwgE9xqN3QrDxs1g2UBtOp1JpVMroGnLsLx8
        C1ZWbhOJ2//KcvxLozmbYdhoBIZZFGM/xeOpu3FZhXVUdZGKrKOq6asYIaM1jCj64HCMveZyuWpLiUT4
        DxKJpD8qyFAIz0sgywokEmoRSSWlJRKJjxnGXlXKwMDgs4V7FgmQ09xZmXTBynX7RnwLNNRlMePLz7ON
        wsqIIwdp93jmZwv3LBJQlPTfSs2bEK/YvoHEEztB6+iid44sLRmjtbTRsUZrKB/Wby4Qj6t3UEDeXvm/
        pL75FjKLf0ImUwJpw77cuO2GoIDT6fUW7lkkIMs5gVhFZRESmVyI8slnsJjM5EQI+D+2lY4rJV63icDI
        iP1lRVlci73xFojkFFsh174DaU4ETUrShY3GiGTTQqS6fTAz41/FvYoEWDb01fx8aI0IAAoI5NSFRN9+
        b0MbIr6wF6Sq1w37jOagwMQNN7BsZG29ZlABj8d/BzvwXQvVNcBXVubB0I07Ifbd98Dv2EE/bwqZIzY0
        QoIZ39AnEAG7fRI8M3NEgvuHCtTX73+UFI9s94VeCAYFKsCR8BWCAsnkEsTOdQG366kN/To7n4Roazsd
        iwKl/SgwOHgNbDYHLVo1NbUVDwSWsljTUWDy1SpYqNwBQUKITELE0UlSYDIUaZiB0NPP6H06pC166Yo+
        LnbdoffhWrjmNMkbPD1uznHy3SIBrO2Y0WqIg9F9JFsfTGJJSAXmD1LxNB1xyguBF1+CAOlH2Oeep5KF
        Y1AU+3CNGbKGg0RWdnupnCim6GF1AZJ8VGAdjIbP0g1X9+yhkwNDDAhCEqJRlU6WJALLwcK774P/lSrg
        XfO0DftwDI4NWa/Rudd374aZE810XexbZ2EhWiyQTC6Tu8tDZUhdt37+BfS1nge3m6WTgkEZuHCcfA8o
        EAlJEGGF3P8RhbYHAhLMznIwcvYC9Nd/REUlKU3HFDI/z9/XBWQ5TQSWaPKUgmUX76355BmSQAxMTc3d
        xw1QBsEN8a/Px1NJhnFCZ2cvfW54CBQzwufj7ukCsVgqu548RqAI/vjAl9LU1EZFxsc94HKxFJTCjS2W
        fjqGPmdyDeFwwhCMVJGAKCaz8bgGW6Fpq+T9huDw4WPw86nz0Ns7TGUslkEq5nS6qSxe01Z4PMFigcIM
        3gr8yu3t7YcDB76GQ4d+gI4OC2AVxdMFAnIZSOS6AnkBQVCymChmwJOKokwSbo5EZ5HkAeZCuURhenoh
        XwdQAJ+QWfCe8T79ftE0mDe6AFalaBTfsBlUms14orm5qGkmJubuVVfXPkYFwuF4VhAwc8uH51VSTnMC
        Pp9gAp6CAnoEwmE5y/NJsqg5MOkwnF4vb5qxMW8+B0KhWLa0UJQDPicMp8cTMQ0K6FeAAhhOs+CTwrC6
        3Zxp7HZ3XoBlpWyuSikmSND7x3C6XGHTjNimdYFHmpp+OXX8+JlzLS1dPWfP/vZre3tPX2fnJWtPz5Vh
        i8XKXLw45BgYsDkRq5WZHR4e99tsU0GGmY4MDd2I9PWNBLq7h3wWy2VXV9flGzino6P/99Onu6245smT
        5y8cPXr63JEjrW2NjSeaGxqO/XjwYGMj7q3/KH047N/2H/D4T+gLwxBnAAAAAElFTkSuQmCC
</value>
  </data>
</root>