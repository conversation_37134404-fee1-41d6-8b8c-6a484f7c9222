﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnExportExcel.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAXxJREFUWEft
        VrFKQ0EQ9E/8An9A+6CthZV2FtompYWKdglaqY02FgqKpE1KQVC0UtHqWWlAi6QQwXJ1TheG5S5vA2IU
        bmBIZrO7b3a5F25ktFqRYTIbyAb+l4HxtVmZ31uWjda+tG7P5LH7LEAs18ukgZmtmqw0t2X39ETOi+vw
        oBRi9V4mDQwCT77tr8wGXAYuiht56r2E76/vb0Hfdx6CBmyd1QDHmKUGjq/aQU81FoPe/HwDoFebO0ED
        ts5qgGNM1wYm1udCDJOPLU0HYhMKmx8D92a6DOgW8GB8YgsMmx+D9rV0H0Ldgp0esHVWAxxjugzggTo9
        qAdSYeusBjjGdBnQg6dbqB3Wv3/5gs2PQftalhrQ6UFMrpvgLXB+CtybWWoA00Lr1Hj9WAO2zmqAY0z3
        IVToHxGosHVWAxxjDmwgBk++7a/8uwb0suEB8stg+yuTBkC+AfW7lMRqvexrIMbJxoJUD+pydNmWu07x
        +wZ+mtlANjBkAxX5ADFhkcY9kEBOAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnGetData.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAVeSURBVFhHxVdZTxtnFE3V9qmq1KhJnvteta99q1pV
        Vf9KpapSpbxUapAiNQl1UkCUpQHaBJsdEwgJxsYQNmOMNwy2h8VgG7wANoSwJaI0yek9nwmBNBkTJWlH
        Og8e33vu+e53z8x8JwD8rzj64+j1VklV7Wdl1xsvVhpbghXGlkSlqRXHgYqVHOaSg1w5yqeXnoC3y683
        GSrrWlHXbkXXwBhGJ2fh0aKIZzYRW9nQBWMYyxzmkoOiyEnuXIkXC3in7FrjLeMNC7xaDNOLGWjxZYRj
        SwhF0wrB+ZQunsQxh7nkmIgkQE5ys4aegPcrTS3wTy3AJ6AITzj6SiCHbyquOMnNGnoCTsu+IZpew7BP
        U3D4p+AYn8bIS4I5zH3CQ05ys4aegDPSJtx/CMwlsxh0h9Dj8MPuHEcvMRpAXx4whrHMYS45yEXOqnoz
        i5zRFyAqB8YCWNvZxc4esLq1i0gig1AkidHANIa9YV0whrHMYS45VjZ2FOd+B/IIqG1Cd79LJnhUCIMI
        zSWQyG5geX0b23uPsPWXYPcRNncfHgHv8b9tAWMT2XsIzSfg8IXQPeCCVRxB7rwCykWlbXAMtmE37A4f
        7oyOi/ogHF4No+Nix+A8xmWggrNJTEVXMB3LyorTCEwvwidD5wpEMOKbUq3vd8l2jPjRM+xBz5BHrHic
        DlxrkGC37J8HfU6/FJ/ItVaGyj05B79M9cRMAuG5tBTPYCaehTa/hEkRNC7TToGuwIysXBMRk2oBdocX
        dhFB7rwCiqpql+vbu2Ef8apkkugLWNUVkOuCD+auPhRdvb6cT8CH5w0l3XVtXTDJg8MmnRj2hjAidhqb
        mIU3FEVAirAYi3L1swtrshXLCEZSsg3y7JAY92QEThHMXHawwzqABlkUuVlDT8DJkmoTUqubaOywovSP
        Blw1mdFyqxeddqeQsRua2u/p2Aoii2uYS6yLkAzCIsglMzLoDuKOM4A2Sz9M5i5cNZqluBXJ1Q1UGptZ
        5KSegFPF1bUycCFkNx8gmbknLY+o/TPf7oNRCI2tXahr60Zjuw2NN+1ovtkrBXrknlUKWmBqtah222WG
        /OEIEpl1ZDceiPAgyM0aegLOlEiQVVzQPeiS1oelvSmk724pQXyY7Pydw/beY2XLHB6L3wVynzEsmF7b
        ghZLyVZoio/bSW7W0BVAlQymDTk8uUEMirU0GS7OAW24KDZMibjMIRsmlA3HJmT/ZWaGPDkb0knKhoLi
        qmMIkPd3LuF121BA7nwCTp27dMVWLwNod75eG7ZZ7oDcrKEn4IOCwiJLfZsM0wtsyCL5bTh71Ia2QeWE
        gku/WlhDV8CVihqkZIAaO2wvtGFIrf7fNvRr8QMbdtiGD2xISyfF2qV/GllEV8CpyxXV6gWiZ0PTsza8
        ITY0W+X/wzZ0KxvG0lnFRU5DRRWL6NvQIAJe3Yb3n2NDDwzl1Syi74LLEvRmbOjF5XLVAX0BhrKqN2RD
        H8idX4CotA6NHbwN8ws4ng3ZUXLnE3D6pwu/DJXWyONYRAx5gspKtNTzbDi7sKqccNiGvnBMhEbk0ywn
        gvm3+50orTGC3KyhJ4CfzJ9+d/bH5sLS31WSsbUTXX1O1caJmaTYTVYubZ9P3EV8aQsLy9uIpsSKIkaL
        shMptWKLFDWaOxUHuchJ7v0aT2s+I4CHBr6vPxZ8/sXX33z/7Q9nmwoKr8TPG4qzF0vKcaG4TPAbfiaK
        noL3+B9jGMsc5pKDXPuc5NY9mPDi8ek9AffqI8EnAp7vSPKl4Ks8YAxjmcNccpCLnHmPZocvHijfFTCR
        HxF8gJDoOGAsc5hLjpc6nP4nV64mTvwDa2S+nyA2nfAAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column3.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>