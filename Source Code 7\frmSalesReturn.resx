﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="Label51.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="Label49.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <metadata name="Label12.GenerateMember" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>False</value>
  </metadata>
  <data name="Label7.Text" xml:space="preserve">
    <value>تنبيه : اذا أردت أرجاع منتج قد تم بيع بعضه نقداً والزبون يوجد عليه ديون سابقة  فسيتم خصم المبلغ المرجع من الديون , أما لو اراد المبلغ نقدا (تسليم باليد ) فيجب أعطاءة المبلغ المدفوع فقط ولكن بعدها اضافة دفعة
 من الزبون بالسالب بنفس المبلغ الذي أرجعته له ( جزء المبلغ المدفوع لك وقت البيع )</value>
  </data>
  <data name="Label6.Text" xml:space="preserve">
    <value>تنبيه : اذا أردت أرجاع منتج قد تم بيعه نقداً والزبون يوجد عليه ديون سابقة  فسيتم خصم المبلغ المرجع من الديون , أما لو اراد المبلغ نقدا (تسليم باليد ) فيجب أعطاءة المبلغ ولكن بعدها اضافة دفعة من الزبون بالسالب
 بنفس المبلغ الذي استرجعه منك (المبلغ الاجمالي )</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnRemove.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAf0SURBVFhHxZZ7cJRXGcYPNGCBKXasljK1VspU7GjH
        Wx10qL39AzraVluRiZlWsVSlN6aggFoBQZOCpaiU0kqVUjSlEm0JFQkQItncNpvsbvaWZS/Z7OZ+J4Tc
        Nsnj83y7C6FiO/aP9sz8sifnO+d9nvOey/cZAO8rl2x8L7H+uFyu/8LpdJqamhqLqqoq6/9IJHxHJBza
        7vXX25z+SNThj7XX+Bs7XP5wyB+oL4pGIhtCodDNDofDVFZWWmNqa2utX7fbberq6s7zjgaEgshArCGy
        PBAItlSc7kJVcxI1kXY4nS547KXEBmedD45YHyqbxmCvb0YoGHSFQ6fvstvtprq6+v83oM6atc/rXRDj
        tOzRPlTEzsBffACd+1ZiZOdtwNPzMbHpamDzHGDbAgzuWoKW/DXwVBxHeVMSteF2JBoiR1wu5wwZmSz+
        tgYkbqd4KBi4P9LYjNL4KOqrjiG56w7gSXb/kcH4WvIbg+TWFOObiZ79kKzPwrmXc+D2elERH0ZjLNbt
        83kXaFk8Hs/bG8jM/HS9f1m0pRtl8SH0HNkCPGIw9hBFfsfu+eQ18jdyMI3qavsL++UZTDzA+rorkKgs
        QBmXJZFoGvH7vDdpOTMmLmlALn1ez8LGlg7YW5I4V/AY8B3O8il2e5VIJCNeQP6eRvWMCf2+wjFP8Pd7
        Bu22fXB0AE3xeCP3wiztB2n9TwPs6POcAXoO52Hi/tSMcIBkDPyDHCJvkiNpVFebzKhPuv8olwoPGiQc
        hfAzZjwafkUZVqYvMiBX2vHRcGhd9CwQcxZjnO6TP0sFstDMJCLB4+QkKUmj+jHyT/IGkQmN4ZKMcl+M
        rZ2PUFMTYp3n4Pf77tLJuMiAGkhWvKk10TgC9G9dgpHv8tGfU0GsYK8TiZ8gp0g5qUyjutpkTNlQJrRX
        /kp208S3DDr2r0ecYvFYwxvl5RowyYAa6gOBpQmKJyoKMZTD5lzCtcR+ojVWYImXEU3ASZRJobqdlBKZ
        OEyUMY3dxwysNhhcNRfNTQ2Idw2ASzD/IgPcnbOjkeiedv7T9sIKDHPt5dwKIBNaUwUtJhVEgn4SJPXE
        R2qJzMlkIVEGMhPYYTD0bYPmN59HKzWCweBDFxnwer2LGhKttV1D4+j85S1IrmDzC+RP5NVpXPuZnNnl
        FCBO4iU+EkijutpqiY0UkddncAkuSy3jcwbDSw3adi9HJwVDofDu8wZUPJ66VbGOs6OdrXF0rbwCE0wZ
        XiQ69/5c9mMZ5dAkGUv/6v/JTH6W7E2NObEkFeN57gNu6o4tt6JnDAg3NJaklNOFl8PTib5RdEb86Pz+
        VF4iHKQMPEtO70wFezel5J5UDBpIPsw7Yf0n0Ds4hEhjmystnSpulysv3juMrlgQrQ/SwE85SHvgtyTw
        XDrauyjHaUAxuAQjPzBoWfdJ9A6PItTQ7ExLpwpfmY9GW3uHervaEVs+GyOPpgZhC7GtAUZ6gC47byfS
        R/rJ2bdwhuiZ+nTXAAMJXtN8afGdgd8b9PNGjW+6HX30FQiGTqSlU4U34JfqQ9HqAT6Mrl2I5nvS4nIv
        thPtCe1q3f3a5f8iunyK0nVdUrqAeOys7GnMMymSGwyitxm07nkcvOfgcrl3pqVT5ejRo7P4ynzxHB+2
        vbwW3i8aDKxPB+DbztpI2hN7iS4l3QsS1NEUuv3UpqOnXS8DPHrWWMboXWXgXmjQV14Abc/iEydWUHZK
        Sj1VphcUFGR3JJnNkBPOr3C9lnOw3gO6kGRkJ1EWMrej7oZJ97513vVMRv9AlDmOH/uVQcM3DPzZczE8
        OoKGlq7kxo0bPy1NYpmYwjKTzKl1e2I8JQj/4m64PmPQrdOwjWgdZYJrqR2NPxLdERJUVlRX2y6ibElc
        Yzi27XEDx01Mf34eeNGiuLi4kJrXkJkZA1OzsrJmkWvy8/M3dLHXUFsIdmbBv9jgjF7FCsaZWBlRcB0t
        CcmQUF1rLrPqo77MXCffhm4upztnAaXB2XciNzf3Pk52LpmVMaAMzCBXsz6vtNTmnmDn9mN7UHYjTXzd
        oOfn/Mj4NYNyM1nBVZcpLY8EVdem3UQ2pr6WOn5iULfIoIJrP9jsgTb44cOH91PjBjKHnM+AShb5IPno
        6tVPLvb6AvKAREEeTt3AGdzOdzo/MAYYfIxC4xSaYF1iQvUJto3RmDIW+7FBzecMbJ/nxvMVKRRsZWWh
        6dOn30iN68iVZBpJFX2MsGhTfIhcv2PHs8t5Vq2BnZUHYLvVoPR6g3q+VuOPcV3XMCv8VuinmAS7eWJa
        eX03rjTwctn+fa2BfelHMNjit2JUO2q6c3JyFik2uYp8gFwomS8ilkwmrtu2dWtOnc9vZWJiqB+h7dk4
        9WWDknkGVdyg7jsp9jUDz1cNnMxQxacMTjJb5YsvQ/zAZkt4lNjKK+LfvPfehYz5MWLNPK11oWQMTDIi
        h1ctW7bslpMnS5w9g9q/fMf0d6Odr9TglvtQ+/DNqFo2B1XZ18L1yBcQfuYBdNtew0RSskBzVy8KDx06
        yDjzyIfJ5W/RuFAmPxD6ZhMsysjsl17ak2231wQ6BpPgVYFxouM6ignrf9WVKtls6j2LEpa8vNw7OVbZ
        nJb5FJ+scVGZ/EBIPDNI8HtB3aYcPHjwszzHT9mrHYW+YNh5urElGGpoCnkDQUdlZdWBoqKiJ/bu3ftx
        9p3q8/mssYqV+RSfrHG+0PT7yiUb3ztg/gMuzHsNlpR94AAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnAdd.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAiTSURBVFhHxVZrbJPXGf6CQjfK2k4FQSqt62jVoUlD
        QtqmovFn7X5UE/sx0Q1N7aR1DH4g2g6tHQO1QGGCjWlZEyAhiXMPDbcQx3HixI6d+JL4Et/j2I6xcW7k
        RgiQhAQnIXn2nM/2ApRu4k/7Sk/snO+87/O8l3P8SQC+Vjx28auE/Ke9vf0LMJvNktFolNra2qSWlhb5
        f7/f9zOfz/tvh93VbrV441ZTYIy4YWv3RJ2dLh2fH/F4PJtaW1slnU4nmUwmGcL30fj/V4DFYpGDCAHd
        wa6dLpd7uF0fg1U7CnNLEC0tBmgNddDpVTC0mNDeEoNNN4aO1hC8Po/P5/O8YTAYJCHmiQV0dHTIWdsd
        to3BUNc1W2sfTJo41IYCKN2/hzK2BbXXX8Ll4TWoGV6L2sFXoIy8DmXnHmhaLsGiGYbDchWhnq4mJrJK
        r9fLMR/k+FIBYqNQ7vE6fx0MRGBpHEZz60WoYltRfUNC+biE8xMSlHckqIi6SQlX+Pn5zeSzC6OZUIfe
        gk7bhg7tEMI9wQmHw75RxHxQxGMFPED+23CgD0Z1P3T+A6gcllAyKkFNsrZ5CSbCOPcIuNaakFB7S0IR
        95+//gz01mK5GpFIZM5ut/1AtDMt4rECxNA5HLbXekJxtDcPoSm0E2cHJVxmZiZBco+YJRFRn0Id0TST
        XG8jjBTRyn2VIxIU9NW7cuHQj1NEuJ+zsFrMg5ivLxXAkgW95kk0+z7BmV5mxHILYgNJ9ESLEJFYhfDC
        u4gs7EHs/kdwz/8U2vSeu/wkhJhzFFHYR0G2SgRskwiG/FVitr4gQCyI0vu7vAd6PJMw2mpQGGcZGaBV
        kE4R00kBTQxsSWRhCfN0TdrQYiHUaYEk14n9bJeBPsUDrEZ0AxwdXgT9g+jsdLwhWkG3ZQHiqHAxs7s7
        NBh238SF7p8jn9mnA8mfDCYybKaA9sR6CpgT3LINLebLAtIVEPu1HMwWQsOZyI2xVZ73EfMuINDtV4lT
        QbdlAWLB6XTuiHVNoc1VjbyIhBpmr73NjAktRcgVYHDRb8sXBORBTWFyBbhP7G9OkTcTFWxDSSgLvKQQ
        8veBg/gK3ZYFcDCe9fq8xQOhRdTwnOf0SGjg4DWx/2qinkEaGVRDASrR38RaCrgnuGUbXDyNKyTXkFzs
        U1O0SvgyhobHU8lkcoIU48pGb/cCnM7OXXRbFmCz2bZ2+UKeaHgM5d7NKIxSAM+8gGYig1XIYFYrmN0K
        aEhknH0Bi0vLFRhcyEcdyZP9XyHvb6RfwzgxxgR4hIWAS+53MBC+D7fbXUC3pABhFLAv1DUwHwh5cNb7
        LZRfo3o6VQ9JCM3sl0kSSwPEINGPxOIQV5bkdWH3F6cxe7839XwAc0sjfJpA++2tvA9YDd4L+WG2wv0T
        xCMT8Hq6jEnmlFlt1pORwAi8IQtOuTJQSQEqlq2SztHZkymaJzfbnTdRxbtAyUQKQjwRzlcRiwzB5wn5
        UtRJ4x3wj57AMHxhKz5zZKCCLain6ioeocj0P1Phntzab/4CVf08AUwkny0o6vw+rl0dhsfV7U1RJ81s
        Nr0X8Mbv9VwNIqfjGShYLiWVK6jaOvoupu9HMZLQYiyh42cDxufbGH4xyUKbXRzAcKI29VyL0XsGTMy5
        0NC3GaUc6Fom8plXQql9CwZiU+h0uAwp6qQZjW1bnA6P83rvNAqtP8KJDvarmxWg6jKKKCJKeDTLWJmC
        qxIu9q8h/WyKHohM5SKXRGU876V8XswECulfRn8Ro8Qn4WPePVc8uzEWB3jxnUlRJ02pVK42GPRFE/1A
        vX8fPtIygItXKYnLGaiCQSpJUEURInhN77qHTkF0Mk8usXgu9lXQr0wI6GIMrp+2SfhQw2qGKzESW0K9
        un43aTOS7El7qqqq6u3B6F0E4xbsb5SQzeu6ggFKifJASgQDKxj4Umz9QwKu3snDGe6rFJPOfUK0IC/z
        M3uPhGOtEv6uz8LQ4A0EfNGFgwcO/lBwErKIDNrTxHqz2dJ37yZQ0vFL7Gvg4FC5EFHM/pUwmAh6lt/P
        9zwsoOdWPnJYMSG2lPuKWXIFicv5PZeJ7FXxUvMfx8wYUKdSqsmZRTydFrAiMzNzNZGlUCiOjPbP4vpI
        EH9WSviErShykJikRSQoZtDTnXz5CGRRwPKPUc9EAbIpVjxXuLnXmfTJ4yx9yESON72MmdtzCAWu4dCh
        Q28x2ReI1WkBogKriHX8vkGjafRjhkcodBa7L0s40szBY/BSBi8k+Vmi2P0UNNFt0Ma2Qx9/B5eDm5BH
        oQV8JvaIsp9h5vtJ/h5j9A07MXUDqK6uPkeOl4n1xH8rICyTeI74zt69e9+02x3yNdfsOYqdFyQc4Ezk
        GJPBi5lhPslOmJgZ1/5GZPMFR8GsFaySaNu/2PN9LPue8xKCvWqI362mpqboypUrXyXHi8S3iZVE0sTL
        CE0MxfPESydOHN/pcfnl8joiZfiAInZdlHBURzIep1PmZHkFmcAZCsihIEH8Maf9D9VsX+1zGBj1yjc2
        f+4nduz4zVYRm1hDfINYtvQbES1diRePHfv0d3abc0kEmJwaR4X5V3ifGe36XMIHtRIOsryHSCawX81B
        q5Hwx3MS/sKSq11/BRZ4VbGVzVrdwLZt215jzO8ScubiBeghSwt4QIhQuGb79u0/bmhs8N4aYyQKGb89
        jLZwNkrM23CycSM+rXseR1XrkN28CeesO+CIlWPm7l35kuyPj6L6/LkaxtlArCW+KYjTeMgeFSDeXAVo
        oiLPnjqV+7bRaArfGOLtJy5AYm4amJqcwfRkAvPkFK8HS1zrj99Eo6bBePjw4dfpK6q50mq1yvGeSEDa
        SXza7XaxLaO8vHyzSqU6xPtC7XV3eYOBa5FQIBZ1O30uvlNerKmp+VNeXt73uHeF8Ekn8j8FUPvXiscu
        fnWA9B/Q6nUe8PktdgAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn9.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn10.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn12.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column4.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column5.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column6.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column9.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column10.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column3.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn13.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn14.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column14.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column15.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column12.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column17.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column13.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn3.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn4.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn5.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn6.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn7.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn8.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column11.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column7.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column2.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column8.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="btnNew.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACN0RVh0VGl0
        bGUAQWRkO0ZpbGU7QWRkRmlsZTtCYXJzO1JpYmJvbjsV3KoKAAAILklEQVRYR4WXaVBUVxqGmySzZJbM
        TGYr50fKEZxEU9boWE4S42gSIxqNE41VWsaJKyASjcsAARQFBBQQWVQ0UTSJoilwonGJG7gjSixLnUlc
        cAVZuukNuummu4F33u/0bdJtY/yqnjr3IpznPefec85VB6AbvwrReII8+Qie6gH5ufzdD1aAM+CGVfjZ
        uYqCz8+BLQqEbZUo3OZt84m3PYv8rWeRV3wWa9muLT6jSMn9Oo1dSBAJHpJWUK5Lyz+qSyX+FeAMuGGJ
        9HHlcnlgd7hgbXFAb2zFvTozw1aiorIGCav2rGQ3PyIqRJO5XZeUc1C67i528b0z4IaVz46kPJ1d8Hg6
        2XbC7elSuHgvOJwe2NpcMLc40dhsw637JuRsPoWuLmBf+XdYlFKWzq5+TFSIuPS90nV3+XxC4A1LplbK
        TZHgk7rcDODuRLuGw+lCq92FZlMbbt9vRvqGchXA5vBgz9H/ITpxRwa7+wkJei98PiHwhpXHZyklQjdF
        XrlXqlpXJ5x8BO3aYzBaHai5b0Tymq9VAAf/XULsPngFsxdvzWSXPyUBIXw+IfCGlasF8BfKtVNaTS44
        hHYPTHwPau42IzZjrwogj0p+1+bowBf7LmFazMagED6fEHjDWsNnKSUjFKQzp9srVWJKfdjb3bC0OnHz
        rhELlpepv5MQHZ0M0iFBOrBzTzUmRxRICN87EegMuGHlfOINEDBShvBK3ZqYON3EwwDtuNtgwbKc/Yj6
        qAQRcTsQEbsDc/4tbFdExpdI5z8jMguBzoAbVtbHJ3nJANoo/UcrQp9Y2lauhBaiN7fhVq0Zl683oPrq
        fVRduofT1XdwvKoGp8/f4vtxSDr/JXl8gFUbT/CSL5OSUq6Ebtj8cVBObHwJpZVZaLZwT+CKaDDYUFtv
        xW0GunGnmSvEiKSsg9L5M0Q2qUBnwA0ro6gCfIzd0xws9kqFFoFL0WprV62FrVnCcGU0meyo17dyo7Ih
        IWu/dP4rogIE1MMB0tdp61lJPV4Zr7uldk1KWmze1iotEbmCG5Tewtkw2mGytiE+QwX4NXl8gLSCY9qG
        4hstRSJTUrdXqEktGuaWdpg4epkBq81JHAznVPJWXmeuOyqdP0ueSikbqUstfVOXUjpS+YICpOQdUQHU
        SP1GrEbrJ/WKnQqR3W2qwVcXClF0eC7SdocjrSwcRYeisK+6ELuPHJPO/0hkKYbYPTd1yTvfEF1wgBVr
        DzFAlzZifzFHyOm1tHqvRWwiMtqKKyXI2jMRR67moUZ/BHb3DXIdNU2HcfhyLjJK/4kFBa/IIfULojak
        pdtfE11wAFkyEiBgmik2+WHklMsWbLS2oPhYLDZXRKPeWgmL6wIetJXiprUA35ozccOSj1rbTtw2HcCm
        w5GIKx5eMfC1P8nL+P3W/HCARC6ZTglAsb9UIWLSbJWpd2D32Vx8fnIxWlxXUG/fi+/MWVhdMhML1kxS
        ZJZMxyXDUlw2pOJey05sPhqDheuHbqBGDil1UgYFSFh9AJ08ik2caqOMVtDERk65gevdwNFfu/9frPxi
        PPT2Stxr3U5JMmXL8EH2JDSbHYqY7HdxoTEWVQ1LcK5+Ma4aCpG4bVTH1IS/DqFKVkRwgPjMr7wBREh5
        s4yYYgVHrrcwhNmGXcezcfBiNupsX+KiPgnVjfFKNm/1u3xv2tHGpRu9agJO183HqdoYnKidR2Kw49R8
        RGYPkVmQA+qJoABx6XtUAK9Ypts75QaNRm67BnMrVpZMRuXtXFwxZCFl21REZ07EXCFjIoycJTtXUGT6
        O4oIkvTJJByomYbt30xGRNaQG1T9nDwZFGBJ6pc8zbq6hXpBtlmOvIk0crvVG61YVDQcVQ9WoOTqGESl
        T8DdWgvuPLDgJlu9kfsAX+IGYxu+vWPGVW7Js1LGo+j8EGyqehWzMge3UuU9Gx4OsGjFf7oDiNSHyJtM
        DtRzr28yWDC/8FUcv7MQ6y8MxpzUd9CoSX1LVnZE34sry3VG8jjkVQ5A3plBeD91kJ0qORuCAoQsTN6t
        AiipiXIi4gaNOr0NDXoL4orext4rc7Hu/GAs2TAWM5e/jRkaIpQXdvqycXh/qTAWHxaOxpoz/ZFxaAAm
        Jw64SVePMxAyf1kpPPyiELnQwJE18GCRfV2oM9jxQG/GhrJkrD84GRsvvIxN1a+g+OIIxb+SxvJRtHC1
        ODEt8S1s/uYfiqLzLyHndD/EfToQ4z/st4WuHt+BkJhEbwAl1qgXmnm6kTo5bpssPPerELH6ZXx6MZwd
        91edZ5P3Et7Cdb4HMmtT48cg68TzWH2KsM049jymJL3YMXTSc8Ppkr0geBlGJ+zi55T3C6hNHck+vMex
        fIDIZ5jBZEF+SRLiPx6OnJP9uyUxuSMxJW40psSORvSakVh1vC8yjoeRUMzMegHhkWHF1Pi+DYIDzI3f
        eSiKn1CRcSSWn1hkTqx8Wu3AbH5ezV5CFn+GeQkl2LqrHFEpEyn6G5YfoKgiDPlnB2F91Uvk77weiJXl
        oVi2PxRT0/piVFTYqd/0eroXNY/eCf1K9uofQvZz2UyefWNGn5UTlvRzxmx8AR+VhSG1vI8ivjQUEYWh
        GPNB3/Zh7z0nh9Fvtb/p+Sx4HL4at+AvuuvGTXIpo5Aj9pmB4b2Gjpjee8ubc0KvjZobytGGYuSsPteG
        Teu95cXX/zCMvyOHUPfIX5/9ZzasnkSPwlfh0X11o6LD5FJGIR3K83yayLOVD4/fkd8TGbH8TP6t+/+L
        w2f21o2Y0ZuXrJ5Ej+IR5Xsk0rlMrYj8kXBKrOFXOt3/AbGCGaaY1ZGOAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnGetData.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAABrdJREFUWEfV
        V2lIlWkUzhZsmmFqIKZFy0ymccjQoWjIqKh+hGK2GqRRJAQRtGJUphYtZpMzpbmWZmWpbZZtXjUtbTEt
        UstGr5p7mnnTe11KbbpnznP4lOp2tSSCOXC4n+/3vWd5znPO+9rnfynz5s3rN3fu3AGurq6my5cvN12w
        YMGA+fPn91u4cKGJ8snXE3Zk4uzsPNjDw8MhNDTULzY2NuHhw4f5xcXF1RqNpvHVq1e6oqKimgcPHqj5
        nerQoUMB27ZtW+Li4vIza1/FzJeLk5NT3xUrVvwRGRkZU19f39zW1kbQ1tZW0ul0pNVq5be5uVnW3rx5
        I++heG5qamq/cOGCas2aNU6LFy/ur5j9PGHnQwIDA6M4w385Q+IAqLa2lmpqaojXqKWlhdrb2+nt27dd
        2tHRIWtw/vr1a/lGCVR/7ty5lEWLFlkq5rsXBweHn3hD9rNnz+jp06f06NEjysnJIbVaTRUVFVRdXU0v
        XrwgBMZZSvZwCMUzEIHjxsZG+aaurk6+57JVMXd+Vdx8WmxsbEy8vb0DS0tL6cmTJ9hEXGsxABSQPX7x
        d1VVFSFIBMYcoMrKSnEGp/gOz0AN3yFw6MmTJ9NmzZplvBzW1tamycnJlUwoysjIoMePHxOC6cwcJYBz
        GH/58qUonuEcaGVnZ0vQhYWFVF5eTmVlZZIAgoQWFBS8mz17trnizlBsbW2/v3r1asONGzfozp07EgCQ
        4I1iCBnDKIwjKDiGdmaI9ygZJ0GwkZWVRXl5eWIHev/+fT1z4TfFnaHY2dkNio6O1pw/f55u3bolGXUq
        UIHx3NxcMZafny9ZQ/GMQOEM37AjSkhIoLi4OEpJSZFAkFBiYqKe29o4DxiewUeOHGnkIIiJKEGgFL1R
        7D179izqTpcvX5aAeE7QsmXL7BR3hjJ9+vQfOQCdj48PXbp0iS5evEipqali8Pbt21+k2MMZ09GjR8XO
        7t27KSoqitzc3GwVd4YyceLEQTx4NNwJBBSCg4Mli5s3b9K9e/c+KMen9OOSJSUlEU9GydzLywtovOOx
        bbwEjMCgU6dOaXjk0v79+8nf3594BFNMTIywGyRDy6HmYDqIWVJSIop1dAw6BaQFgkhkx44dFBISIvZ4
        DSS0VtwZiqOj45Dw8HDtiRMnBAFsXL9+PfFUFEjR43AOaK9fv/6BYg1kwxACAoAeewMCAujYsWPEiYlN
        d3f33xV3hjJjxowfeKN2+/btYhQMxqaDBw+KAWQJ1oNQ6JQzZ86IgrDIGAFgXvAZINBzOaUMaEvwijmg
        Zw7YKO4MZfLkyQN5U93OnTspIiKC9u3bR8ePH5e2gnFkCUKhvRAQkILiGXWOj48nniNdfEFgu3btkvdI
        in/fzZkzx0pxZygTJkz4jmHXIGs/Pz86fPgwbdiwQTIBqVACZIgaY7KBA1AggzUMKZySmAngzZYtWyQJ
        lIFLK2hNnTrVOAktLS0HcvaVvr6+dOXKFXG8efNmOnDggLQjZjwGEfoaSABqKDLH2t27dyUAIIAO2rRp
        E4HQKA9scHId48ePt1DcfVpmzpwZyC2jB6RQwI5fDBS0I5zAKeBFKaBoVawhyMzMTDp9+rTAjr2d79GG
        fINK5fOm+7sBozCUj+RMbkE9yuDp6SnZgf2AHe2IQEAuEPXatWukUqnEOXiCUkBB4L1790rt0RHcfuVW
        VlbGz4H3ZdSoUUOnTJkSxdOrfd26ddKOmOUNDQ0SBAJIT0/vmnp4xhruDbgjIEhMvY0bN9KePXtAvCQL
        C4tfFPOfJ+bm5n3HjBljP2nSpNiVK1c2MKx6DBkcRGhDQA5ooWhJ1Bm1B0+YP/q1a9e22NvbqxhRZ3Y+
        QDH75TJs2DATMzOzoWPHjnWcNm2a39KlSxNWrVqVzw5qtm7dSnwBRafoVq9eXcg9ruIDLWDcuHFLRo8e
        bTZixIjeX0qNyfDhw01Y+3OJrIKCgmQO8HX8T65v/5EjR379a7kx4fY0B+nACSaar7L87cTTy8ccYxl3
        Pi8v728TABPRne8L2rCIaG1oeEQTBhVGdmBQcNvfQZE6f/+/tNwxtbw+UNnSO+EhkszDB//pqHmkqtmx
        mtfU3Gr1z58/53tgFZWWVVBRSRkVFPH1vaCY8v8p7JoVPKSKP97L3ZKomO9Z+Hwv7bzPo/XgFJdODBu0
        W3ealpbWdXvGtRx7cX5wYKWK+Z6Fo87ig6O+UzkbDUOv4cOpNSwsTAYTZj26AAcWDhscvzi2MbJ5/Gre
        3w9lBDIV870XvlC68ZWqujvl/wWLeTaYKlt6kD59/gPFDyosNf6YmQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnSave.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAAt0RVh0VGl0
        bGUAU2F2ZTv56PkJAAAJQElEQVRYR8WWd1TUVxbH3ZItycYkpm39d/+Ku1FRikjvvQ0ww9AZGJgBBhh6
        GRh6R6RIQEWxIGKkKKLYgsEaY8EuKEWRCCrShKjnu/f9hiEhuMecPSdn3zkfHo+Zw/d77yv3LgHwf+Wn
        41fEr/9HfvMzUH+X6Swa7IO3iN8Tf/wJb7+Bd34m7Lvs/zMzC0ywxVth6Vsy5Hk7X0YX1COmcDfiiuvB
        F6fD1iMGtoIYeIhSkV+2G7ml9cgu2YHMou1QFmxDal4tUrI3IzGjBnHKKsQoNiIqqQwWjsFw8pbD0UsO
        B49I2PFlL614kh2kxUywgOcHW/whInfHbNPxK2g50Y3mzm60dV2Hpp4HjCw8YeMcDM8gJZ5NzWKMeDqp
        4snEDB6Pz2B0/DlGnql4NDaN755Mw8w+EFV1+1FZ24ryTc3YUNMEW7fwl6TFssGyMD/Y4u3w7Do0HbuM
        3E3tyN7cjpK6o3MGhLBxCoYwMJUTvNr/BN19j3Hl3mNcvjuKS70jWKllTlhipTZB8/2RCRjbiiCNKYE4
        qhCBEXlIpixZu4ayk/funOb8YIt3wtK3ofHIRWRVtxEHULytA2v0BDA0F8LaSQx+QAoX3cWeEXzb8wgX
        7jzCN7e/w/lbw1ilY40Vmk4k7gx72q67Q89gaOWHkLgiBEXlQRSRi/jMarWBpcRvmbB6MAN/kio3o+Hw
        BaRXtSKjaj8Kt3Zg9To+DOYMuPkm48HoJM7dHMbZG8M4c/0hTl8bwqmrQ9DQtYPGWh4ZdoetezRuDTyB
        noUPQmKKERhJBsJzEZteBSsXKTPwHrHYgERRg11t55BW0QJlZQsKtrRjtS4ZMPOAlWMgeN6J6BseR1f3
        A3zdfR+dlwfx1aVBnLg4QMJO0DH0wFpjL4oyCldpW3TNvBAsL6LocxAQloXo1ApYOEuYgfeJRQbeDUmu
        Rt3+M1CUfYnUsn3I33yQInOHPhmwdBDB2TMed+4/xYlv+3H8Qj+OEh3n+9Bx7h60DHjQN/eFkbUIFrwI
        XLj5EDrGHhDLC+AXng2f0GxEppTDnG4Gab3WwNKghErUNnUhqaQRyaV7kVPTSml1g56pABb2IrpKsbh2
        bwQdZ+/iENF+phcHT/ei7VQPtA3dYeEig7mzDGZO4ThNGdIy5NMBzIdfaCa8JZmQJW6AmaOYGfiAWGCA
        LZYGxJZj095OJNIbkFjcgKyqFjpcrlhnwoe5XQDd42hcuj2MA109OPD1HbQyTt5GC9HceQunrgyi6/IA
        Tl4aQOfFfmjqu3H77yXNhFdIOsLii2FCV5O0ls1pzg+2eM9PXoKN9ccRm7cT8fm7kFnZBEuXMKzQdsEK
        LRfomfnAyCoABlb+0Lfwha65N3RNvbDWRAhtSre2oQCaFPUafXc6E67cNQyg/fckcaFYSQeyEEY2IrUB
        9urOD2bgfS9ZIcq2d0CevR3ynO1QlDZAWd6I9LK9UDI2NNLM1o1I27AHSvo8taQBEakbIYkpQkh0IcQM
        2vcgunYiWQ586fAJg9LApzckKDKfM09aHxKLDXhI87B+y0FEZNSqyKxF1BxyNmcxts7/Ls/cgghlDXwo
        Qh9JOrxpZtF6BjOU8BCnQcDERQrw/RV0E3JgYOnHDHxELDLwgXtwFgqqWxGetglhCoJm9ruMzUqa1aTV
        ENX0txoE0CsnnBNkaWbRenARkygJuwcQ/slw80uGb2gWvQ2+PzYwX5CYgWUugenIrmiCJLEKkuQvIEn5
        AlI2J1dBmkJ/I9gcmrJRtU6sIDElBEyMUsxEBSzaOWEmyvNLAs83CS4+CXQQldzbQFofEwsMsMUyJ98U
        KNfvQVBsBYISyiGOL4cX3V8PikxAEQrYzKWVxIIU9I8T4OgZTdeTVbsoeoIjYc9nVS8CNm4ymiPB80nk
        xJ284rnM6Bh7MgOfzGkuMPChnWciFIU7ESDfwCGSr+eimn3xEjOzKp4T07MvMDXzEhPTsxinysgq5DOq
        jGOTM3hKVfExg6qihZOUhOPIZCwcBLHcOdA2EqoN/I5YaMCaH4O4nG3wlRURxfCjmaWPCU8+f0F8/4Mo
        K8VUhpnQ6BhjGiPEo6dTXCkefjJJ5TiYe7zs+bHcG+JK2dA04DMDnxKLDHxkSW94NBUkoSSfUp8HT2k+
        nH3iuagnSLz52C18eeQm9hy+hk2NF1C56zxK686guPYU8qtPIrvyBNJLj5L4FIYfT8LYJpATtnWX05bI
        4SyMp+Lmxgz8mZg3wH4wAx+bOYcjnA6cICibOiEVDsJoTM+8wPj095RqBjUiE7PzaVZFrY6cCROjUxgi
        AwaW/rChoGx4UbB2ofNB26CxzvW/GzCyl1L9LodrQAbcROk0UzvmHokpin6cxHe3X8PO/VdQVf8NSref
        RQmLvEYd+TEoig8jsaCdEx+isr2OXk4mbE31wcJJRt1QNFbquDADfyEWGfhEz1qMwKj1cPZN43DxVcCK
        F8alf4y1YCxy6ojYnrPIR7jIWfulivwhg4SHRibxgNCm0syEGeaO4bDmRWKFjtNrDbDFp7oWgfAPK6BT
        mwwHhjAJ5nSSWfRjJP50fBajXNp/6Ps4ceIhRc2Jj05w4qwl09IXwMwhnAiDqX0Y9QIR+FyLM/BXgjWm
        Cw1om/rRi5ZNBycedgIVJnZiVHT2I+dQDzLa7iCx+Qbi991AzN7riG68jsiG65DVX0PYzquQ7LiK4Lpu
        BG3rRuDWbjpw7lT9QmFqFwoTWynMyci/1zgyA38jFhvQNPamlyydTmwsrN1iOAypwdh45hEKTgwh88gD
        pB0aRMrBASQdGED8/n7EtvQhuqkPkfvuQdZ4F+GEtKEXkt091KDy6CZIYETiRjSbkpnlaxxea4C7hmsM
        PF+5+adSCZbDyjkKloQedTllXcPIPaYSV7QPIrltEAlkIK6lH9HNfYgi8Yi9KvHQPb2cgZD6Xq5JZcJq
        jG0l+Gy1/QvSWvAOsB9cR7Rc06FolR7/lQY1oiupFWNo0j56FnbBNesrOKYdhW1yB6wTDsEi9iBM5Adg
        GNGKdaFN0JHsg6a4EatFe7DKfzc+99mF5RoO+EzDnmO5ilf//JdJPmmpWzLOABvsF+aItcvsmWSHhKXp
        Tfz9DfzjR7A1u/9MfD796sEW6kyw7WBf+CVgQaojX2BAPdQf/NLMjSVL/gMGC1sK0EICNQAAAABJRU5E
        rkJggg==
</value>
  </data>
  <data name="btnDelete.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAAH1klEQVRYR8WWe1RVVR7H
        rzWTJSimiDQoXBB8XEF5KAqooMhDAVEBHQcZy1R8BKmAgoCggshDFDELRkB80SJsQT7CUBDE14gilK6k
        iIsP0CBFc6TVxPnO73cu5ybc66r+mX5rfdibffb+fb97n733uTIAfypaG/+fqP5ojz7EK91w/Y8E9+89
        XiPHywxIA/9K9O2G67/XCPd5lXiNeKMbzsFtPcZrMyAN7lu5cGGoMnlHpzI5qbMiIOA9anu9+1mfG4sX
        y5jaxYEqAlXwM+IvxBtlfvNDGhMTOxsTEjpL585dR239up+pTfQ2IIm/fm6Bf+SdvWn47706EeWeFJzx
        8wujZzwb0UTXg69kIq3Ml9SkFu93ytd3fdOuJPysvIafm2rwHdVPzfGNpmc63X1EE9oMvFbu57e+mcR/
        ulmNJ6WF6Dh+BD/VVaApIwWlvj1n8ktLveyX+/VUVYvrfObtE9aYnoTnV79A+6EP0JadimdVxWjcuR0n
        vLwjqA+vJL9ODQOvTJLLdRvi4zufXzuDjpLDeFyUK/KoKAfPa8rAiY97+fBKvDgTtXjxbK+wb1MT8J/L
        pWjLy8CDfUl4uDcRrRnb0PH5UdyKiu4ca2ioR315FTUMvOplMXLgxWUrnrQX5eOHgqxfOfoR2o98SIlP
        gQWKZ81mE7oEbzRG95jHrPCG5G14duEk2vanozWThbeidVc8WtJicTc5GtVLlz9zNjYZzFqE5goQffdZ
        2bxdGRTY1XYgE2353RzYg+/zmAz8eL4EDTu24hM3z3DqP5ApdPOIuJ20BT9WleBhVipadm3B/fQ43Cfh
        e8mbcGd7JM76enftVox/l/rzPtL6Cngp2Vm/3WMsV1YsWtj1IDsND/+1Ew+zmTQ8yCI+SsHTs0X4OiEO
        h6a7Rh1ycY26lbAZTys+ResHSbi/czPupsTg7g4STtyI5m3hOO3l2ZVsPnoN5e5P8JFmLQ0DHNL71E0Z
        MWp12fx5XS17t4uJuWyhZW3ZQ2Qk4DG905vx0SIdZYViGy8zz/ZOwgY0bw2HMm4dTrrNFBLk5nyMBxBq
        cQ4NAzkWCi64A3fsv93EfPXnPt5dd9PjcY/ZGYd7aTTDVNUsfyg+gEcnDlNbHM10A5Q0W2X8ejSRcGN0
        KEpcnIW4v5mEUC7eeLxXelxmGgay5SNlTyuOcVUyMSB+mHzNCU+PruakKNwhmmmGzYmRUNIsldsioNwS
        hiYWjV2L72JCSTgE325chSInRyFqiFEo5eB9ohbfa2RKhSo0DOwbZibr+KJA1nG6gP9Vm4geahRyzGGS
        cHtNEL6JCEbjhlVojFqDRkkwYiUa1r2L26FLcGtZAD62Hi9EDBr6Po3tId5ekifbPXQYVVWhYWDPW8ay
        RycOEvmyR8fzuUk8GYR+mL5hWKGtjVC/0BNfMgs8UB+goi7AHfX+7qjzd8MRxRjhPb3BfOEYENKl06et
        MEvG7BxsSP+qQsPASRc3Wfun+2Xtx/bzv7wCPJiTDMmwsd98cclifBU0Fzfmu3UzE7XzXFE7nxDLmaj6
        ewDSrOziacxQQjpyfR4czpQxqQPZlyo0DHw2xVX2fcE+rkrivHxvZlrbb7q0fCmUyVGoW+SF63Nn4Lov
        Mx3X5kzHdR8XKp1R4+OMbzatxvklQUi3nBBDY/nS4RUUTbTkpMk+mTCFqqrQMFDs4MKFJM4DB+0eNyG6
        +p1/oikhHLULPFHjTULe04ipqPGaiqte03B1NpUSs6bg6/DlqAxchFSFTSzlYBPqL+nHto5UqELDAEUP
        8XRL25iqoEA0xq1FrZ8b/k3JRTydcIXxYBy7ccAVdwdcdp+My26TcTNkCcoXBCBp1LjNlEufUO8HQoze
        BnqIpylsYug2RAMt6TVack5+RUxOIiRwaeZk5I0wFZiLMybhkqu9ihkTqZyIi1TWBy9C2bx5SDC3jKOc
        Q4geJrQZ4Hc+KGW0dcxZfz9ayhW01C6/Jic48QWXicgzkwsrdPRimQMWI4Rq5wnUbkfY4oKzLaqn2RG2
        uPGOH0q9vbHVbKxkgieoYYAb+B31T7SwCjvtMwe33n8bV2ZNpYSqxNXOlJw4T4lzTU2ENboDN1J/I2a1
        7sDIPPMRQtUUa5xXM16kymkcrv3DB8dd3RErH72B+vOtKH7KexvgS0f/qJ1zR11wIC7TO66eaoPzIpSQ
        qHKyRo7cWBLn88Q/ThgDNpFrZiqcc7BCJXFuMmOJc5NUXPbzQL6V4xPqy8dT/CZoNXDQ2qnt4mxnElO5
        r3JkKBklzTEZLrAQ9WNx6ecZw3WDVbp6UbmmcqHcXoGKiYT9GKjqY3BmoiVyFfbt3I/QaoATDYg0Hhlc
        aGMvVDqw87GoIMqJ/cbDeotLm4nhOrcNXamjtylXbiKcsRuFs0S5LZXE4VFWwjpD09XUh69n8Ui+aICD
        E/Em1I8wMl9bYGUjlNkpcGaCAllGRlrFa5cFyGqX+lO1hwlDNpE9fLhwevxIlI6zwEGLsUKogZx/RfHy
        SxeTSruXAX4gXr1hb5mt/FBu1U48CtbR4+85i6uPUbKevuxFuK37mbgSy3X0QjOHKx5nDlO0hxiY8I+R
        HlczoWGAQ0rCLnm38iCGl01yLg5+Sbw4/k2CvzwM19W3ISGGNgMcUhI+KvxKGK7/lrgUv3v8ywxIwZ1f
        5I/Gb45XG/jzgOx/2ZzKgxEwf90AAAAASUVORK5CYII=
</value>
  </data>
</root>