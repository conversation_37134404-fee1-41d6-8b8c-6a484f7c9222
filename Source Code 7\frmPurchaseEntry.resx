﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnSelection.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAALEAAACxAcYtSY0AAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwa
        AAADxUlEQVRIS8WWSUwTYRTHO21tC1QaitjE4EIFOdQFLchmUBOUqiiOS4waUxYxqYaEi/vBBGLiAU0A
        tR5UokEN4QBoiRhXojOdWqYUlLoRovbCEqyBhCpqx/eYksAwoBIS/8n/MJ3v/ebN+977ppL/qUiwRUYQ
        90JD5LRGrWBDVHKaICS18HseWI2LpiMZ+HREuLL1XPHql73PDgxy7Ye4UX9+sO/LqcIERh06yw3rDoMJ
        DPpbaeQy4v7x/BXML3chN2DP81eeTGNN6dF00tK59JaMBdS1krVuf0vB8LDr4K/92XG0VCq9DXEqPnxq
        yRBeez6zAzM9WZDgiNSGd1gsFntTU9NHlmV9Nputy2w201qN2lNxIs2F6y4cS2WlUsktiP/jm5zGzDFo
        /ep5DEmS9r6+voDP5+OE7u7u/pGSksLk5iyx4/odmTE0xOfyGHFFRmiUrVgWzBzhYmChjUaj43rJOveQ
        M384RClvB46Cx02UBTcUa45lmSxzob1e7zdtRFjHT3dhIH97PL4FyeMEgla8i91ScTyNxZqLwSazyWSi
        HlzZ/N55h/QC6hJPFAj6fKSWpjXRFGzop56engDU+KUYUGir1dp+ZK+B+s4eDMhkRHMQOV6a2QonPiBp
        aZTd5XL1ezyeAYPB4BADCl1XV/dh1wY9hfEqpfxFEDleOKG4IBv6vLGxsau3t5fT6XSvxYBCl5eXu4sP
        LKNhowMEQTwMIscLxx8nFDsC+xwDk5OTmZqamrdCoNAZGRn08xvbupqrtnYBqpwnTlQejj9OqDZC7cE+
        7+zsHGhoaHgvBh01rBmcqw17FWg7xO3eqKeAk8XjJkqNZwuOP05oamrKH+vf39/PxcXFumyXsjw+yjyk
        VEhdwJHzOHEd3r8ldmQvzDnxdGKikYE+94vBMfPYxfqWMxajA9enrtAxEI9n0pTHBYEHF54tGFRVurZN
        qwnzYJ9DK7bV19e/q6ysbMWaR0FZbBdNb3Bd0V6DI0E/h9maFOOEM6kaOEoeJy4VLLq5MzPGjuMPE8rh
        EGGf78hcZMduwQ3Fmn+lzP70BB2zKibK/rR0e6D5LMkVZS9nYRYeA0fL4yZXHgxfawEZT7fUkF4YopFv
        gd9ZwOED9mTpKag5fg9ub0pcSCN81GW5aZ0KuawF7s0fIU0hfNUcsBWyeqRSyl5Anz+B6wrwZjBuKJRV
        Uo2Zj33I1SPrP0NMPdyfESmxLGW56e/GPiRUIXcE78+ItFAWJ2aO8KPkyjapRHI5eG/GFI1lgcydALfC
        9bT/FPyDJJLflD0YEKSXmD0AAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="Column1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column2.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column3.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column7.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column4.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column5.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="Column6.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="Button1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAAOwAAADsAXkocb0AAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwa
        AAAFRUlEQVRYR9VWa0xbZRiml7XdRhEYUqDruMyxAmUGAaFiHDXg5qVcSqYJUxk4iYlDzOKMjsRsgcW4
        mOjcxCwmZP6Sbb8My3Sbw2gWTy9cWmWFMEAysIMK5eYEBlLf5xxOQuqhO1NZ4pM8P6Df9zzv973v974n
        5P8ENdFMrCHWE48SXycWEFXENUMh8ZJEEjKt02x0FuRqmcriZOvL5m2M6dE4JjpS1SUJCZmiNeeJmdjw
        XyGJ2LpBJRs88W5e5++2qgX/T9V+Ifp+qJirq85wyOXSW7TnLHETBP4NTHTikbcqdjgWna8u8UbtZy2e
        hgPZjoqiZGa/Rc8cP5jT1nvhhXH+9z8cVQuWgkQr7e8nprNK/wAmqVTivfDprl6IIoBjNdntKqXsplKp
        HM7MzLSVlZUxRUVFTHp6ul0ul3vDQhW9Zxryu/hATtblOSUSiZe00jhJ8UjEyVtOcua3Wl+cjopQXddo
        NF0tLS2DExMT/kD6fD5/U1NTt1qtvqFPCm+fsVbOY++HB3OcpDdAvKd0fItr581VCtlweXm5DSZC5is5
        Ojr6p9FotEeEKXsoFXegUZi72Uaapznpu6MABYcrB3FymAuZBWNGRoYjQx/FHsJ3bd8spXOctA2cRXB8
        88k7eU5sRM5x7WJOHsiRkZEFqpWbFxt390GrsiQZRXmCs1gdasr9FP/UUHCr5VwM6+vrO3QxoS5odX/1
        PIoRpHaxOp7TxW5kT99xzuJRqVRDQsJi6fF4FunqZ6YYriBxIPJ4iLMSxgF0OCyup3eelZXF5n5sbMwf
        ExPTE2gghtHR0V0ty085IS6sgzwsnJUwGqpK9FYs3leczFgsFgYiTqdzSqFQeALFxTAtLc3+8duPdUIz
        d4cGdVDBWQnjKHo7Fu8vS2HMZjMbgNvtvk2NZiRQXAz1er2jse5xtg6y0qIRwCuclTBe25kdxwbwwZs5
        7ehwEEEKZDLZuMvloj+FjVZjZGRk9+XTz/RDE/VFHrs5K2EUaCI3sK0UvR3tlX+CJpPJWlhYaA00CMbB
        wcFZqUQyTQ1pcclV7V8nl46Qx1bOShhKeiOTmGoIAr0d7RViAwMDt6nN/uL1egXNhFhTU2NPSQpvg5bt
        y9Jh0u/jbILjPEYqNmGwoLejvQoZBCMClstk3rbm0l+hZX4iHvk/zlkExyOY5xip2IjBgt4uZLIaqWaW
        tFqtq+TJBBs0hq7snaIGN0HaWs7i7mjGPMdmTDUMFipIBu1VyHAl+/v753Q63c/b4h/oxCxB7lO3RrST
        5lVi0C64EhidfacO57HPB1MNg4V6+xDaK3W4vwWCgqutrbXj2otNCVb+A6Z6T6p9vVLWp16v6CHNL4gK
        GIiBgT4mRjHPIQR+3fj0DfR2quwZdDiDwWCjd26np+bG/6jg2tuaLcNYi5PDXC6Tes4d2jV++UjRnS0P
        hqITthIjWAcRSCH2PmXcbPNdq5jlA5n6sXIOX0ofHTJ20i114p3jqfG/D10pn8S14+Rk7vv+WKkf/K6h
        xG/cHoNvAzcxEQZigGg/p6HyG0YqphpvFEg8NXN+vJUKbpL2XA1Vreu+dKRong+A50v5yR20xkNrsmEg
        FsnEz4hDmGoJ2rCOHEO0LSstyrYlNrSTmswofiPiqcUTUXBncO04eWAQdXsyry8HEUu8Z6QSnyVWEtHb
        S4j4fA8ECq4V1x4YAPhwwib0hjfYlWuIcKJ7787tbYEB5KdrGfrtMLtqjZFI+fDg2nnzi++Z5xUyKVJm
        5JasPbKRc1x7vkHLLJu/z/10/4CCQ87riPft5CIQEvIX+AiNmSJhv8gAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="OpenFileDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="Timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>106, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABcXFyIXFxciFxc
        XIhcXFyIXFxciFxcXIhcXFyIXFxciFxcXIhcXFyIXFxciFxcXIhcXFyIXFxciFxcXIhcXFyIXFxciFxc
        XIhcXFyIXFxciFxcXIhcXFyIXFxciFxcXIgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFtb
        W9ZaWlozWlpaM1paWjNaWlozWlpaM1paWjNaWlozWlpaM1paWjNaWlozWlpaM1paWjNaWlozWlpaM1pa
        WjNaWlozWlpaM1paWjNaWlozWlpaM1paWjNaWlozW1tb1gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAW1tb51xcXIhZWVkUWlpaT1xcXFBeXl4TWlpaY1xcXDJYWFgxWlpaY1lZWRRaWlpPXFxcUF5e
        XhNaWlpjXFxcMlhYWDFaWlpjWVlZFFpaWk9cXFxQXl5eE1xcXIhbW1vnAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABaWlozW1tb1lVVVQNaWloRWlpaEVVVVQNZWVkUZmZmCmZmZgpZWVkUVVVVA1pa
        WhFaWloRVVVVA1lZWRRmZmYKZmZmCllZWRRVVVUDWlpaEVpaWhFVVVUDW1tb1lpaWjMAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABaWlrLXFxciFxcXEhaWlpHXFxciFxcXIhcXFxIWlpaR1xc
        XIhcXFyIXFxcSFpaWkdcXFyIXFxciFxcXEhaWlpHXFxciFxcXIhcXFxIWlpaR1xcXIhaWlrLAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAF1dXSlbW1utW1tbO1tbWztbW1utW1tbrVtb
        WztbW1s7W1tbrVtbW61bW1s7W1tbO1tbW61bW1utW1tbO1tbWztbW1utW1tbrVtbWztbW1s7W1tbrV1d
        XSkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFtbW5haWlozWlpaM1tb
        W5hbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1pa
        WjNbW1uYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAW1tbmFpa
        WjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtb
        W5haWlozWlpaM1tbW5gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AABbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1pa
        WjNbW1uYW1tbmFpaWjNaWlozW1tbmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtb
        W5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1pa
        WjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5gAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtb
        W5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1pa
        WjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAW1tbmFpaWjNaWlozW1tbmFtb
        W5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tb
        W5gAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABbW1uYWlpaM1pa
        WjNbW1uYW1tbmFpaWjNaWlozW1tbmFtbW5haWlozWlpaM1tbW5hbW1uYWlpaM1paWjNbW1uYW1tbmFpa
        WjNaWlozW1tbmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWlpaIltb
        W7taWlpKWlpaSltbW7tbW1u7WlpaSlpaWkpbW1u7W1tbu1paWkpaWlpKW1tbu1tbW7taWlpKWlpaSltb
        W7tbW1u7WlpaSlpaWkpbW1u7WlpaIgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AABaWlpmW1tbo1lZWS5ZWVkuWlpaZlpaWmZZWVkuWVlZLlpaWmZaWlpmWVlZLllZWS5aWlpmWlpaZllZ
        WS5ZWVkuWlpaZlpaWmZZWVkuWVlZLltbW6NaWlpmAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAWlpaM1xcXJlZWVltWVlZLllZWS5VVVUMXFxcOlhYWB1YWFgdXFxcOlVVVQxZWVkuWVlZLlVV
        VQxcXFw6WFhYHVhYWB1cXFw6VVVVDFlZWS5ZWVkuWVlZbVxcXJlaWlozAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAABbW1uYW1tbhFxcXDJbW1stW1tbLV1dXQtbW1s4W1tbHFtbWxxbW1s4XV1dC1tb
        Wy1bW1stXV1dC1tbWzhbW1scW1tbHFtbWzhdXV0LW1tbLVtbWy1cXFwyW1tbhFtbW5gAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAFxcXJZaWlqbWVlZKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFlZWShaWlqbW1tblQAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAXV1dC1paWmZbW1vJXFxcqllZWU1VVVUGAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVVVVBlpaWlVbW1uzW1tbyVpa
        WmZdXV0LAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABdXV0sW1tbkVtb
        W9JcXFyIXl5eJgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAXFxcJ1xcXIhbW1vSW1tbkV1d
        XSwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAZmZmBVpaWlVaWlq6W1tbuVpaWlVgYGAIAAAAAAAAAABgYGAIWlpaVVtbW7laWlq6WlpaVWZm
        ZgUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABdXV0WWlpadFtbW81aWlqeWlpanltbW81aWlp0XV1dFgAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFxcXD1ZWVk8AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA//////////////////////AAAA/3///v8///z/v//9/5mZmf/ZmZv/2Z
        mb/9mZm//ZmZv/2Zmb/9mZm//ZmZv/2Zmb/9mZm//ZmZv/2Zmb/9//+/+///3/P//8/z///P/P//P/8f
        +P//5+f///w///////////////////////8=
</value>
  </data>
</root>